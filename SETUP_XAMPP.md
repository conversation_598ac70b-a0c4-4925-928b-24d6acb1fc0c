# Configuration XAMPP pour Oussama Watches

## 🚀 Installation et Configuration

### 1. Installer XAMPP

1. **Téléchargez XAMPP** depuis https://www.apachefriends.org/
2. **Installez XAMPP** avec les composants :
   - Apache
   - MySQL
   - PHP
   - phpMyAdmin

### 2. Démarrer les services

1. **Ouvrez le panneau de contrôle XAMPP**
2. **Démarrez les services** :
   - Cliquez sur "Start" pour **Apache**
   - Cliquez sur "Start" pour **MySQL**

### 3. Créer la base de données

1. **Ouvrez phpMyAdmin** :
   - Allez sur http://localhost/phpmyadmin
   - Ou cliquez sur "Admin" à côté de MySQL dans XAMPP

2. **Créez la base de données** :
   - Cliquez sur "Nouvelle base de données"
   - Nom : `oussama_watches`
   - Interclassement : `utf8mb4_unicode_ci`
   - Cliquez sur "Créer"

### 4. Configurer le projet

1. **Vérifiez le fichier `.env.local`** :
```env
# Database Configuration (MySQL avec XAMPP)
DATABASE_URL="mysql://root:@localhost:3306/oussama_watches"

# Stripe Configuration (à configurer plus tard)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Business Information
BUSINESS_NAME="Oussama Watches"
BUSINESS_EMAIL="<EMAIL>"
BUSINESS_PHONE="0603357867"
BUSINESS_ADDRESS="Casablanca - Sidi Bernoussi"
```

### 5. Générer et appliquer les migrations

1. **Générez le client Prisma** :
```bash
npx prisma generate
```

2. **Appliquez les migrations** :
```bash
npx prisma db push
```

3. **Insérez les données de test** :
```bash
npx prisma db seed
```

### 6. Démarrer l'application

```bash
npm run dev
```

## 📊 Données de test incluses

### Produits
- **Rolex Submariner Date** - 8500€ (Luxe)
- **Omega Speedmaster Professional** - 3200€ (Sport)
- **TAG Heuer Formula 1** - 1200€ (Sport)
- **Casio G-Shock GA-2100** - 120€ (Sport)
- **Citizen Eco-Drive Titanium** - 280€ (Casual)
- **Apple Watch Series 9** - 450€ (Smartwatch)

### Codes promo
- `WELCOME10` : 10% de réduction (min. 100€)
- `SAVE20` : 20% de réduction (min. 200€)
- `FIRST50` : 50€ de réduction (min. 300€)

### Compte de test
- **Email** : <EMAIL>
- **Mot de passe** : password123

## 🔧 Commandes utiles

### Prisma
```bash
# Voir la base de données dans le navigateur
npx prisma studio

# Réinitialiser la base de données
npx prisma db push --force-reset

# Voir le schéma généré
npx prisma generate
```

### Développement
```bash
# Démarrer en mode développement
npm run dev

# Construire pour la production
npm run build

# Démarrer en production
npm start
```

## 🐛 Dépannage

### Erreur de connexion MySQL
1. Vérifiez que MySQL est démarré dans XAMPP
2. Vérifiez que le port 3306 n'est pas utilisé par un autre service
3. Vérifiez l'URL de connexion dans `.env.local`

### Erreur "Table doesn't exist"
```bash
npx prisma db push --force-reset
npx prisma db seed
```

### Erreur de permissions
1. Assurez-vous que XAMPP est lancé en tant qu'administrateur
2. Vérifiez les permissions du dossier MySQL

## 📱 Fonctionnalités disponibles

✅ **Catalogue de produits** avec filtres et recherche
✅ **Système d'authentification** (inscription/connexion)
✅ **Panier d'achat** persistant
✅ **Liste de souhaits**
✅ **Codes promo** fonctionnels
✅ **Gestion des commandes**
✅ **Interface responsive**
✅ **Pages de contact**

## 🚀 Prochaines étapes

1. **Ajouter vos vrais produits** via Prisma Studio
2. **Configurer Stripe** pour les paiements
3. **Personnaliser le design** selon vos préférences
4. **Ajouter des images** de produits
5. **Configurer l'envoi d'emails**
6. **Déployer en production**
