// Configuration admin pour Oussama Watches

export const ADMIN_CONFIG = {
  // Votre numéro WhatsApp (format international sans le +)
  // Maroc = 212, donc 0603357867 devient 212603357867
  whatsappNumber: "212603357867",
  
  // Informations de la boutique
  storeName: "Oussama Watches",
  storeLocation: "Casablanca, Maroc",
  storeEmail: "<EMAIL>",
  
  // Messages personnalisés
  orderNotificationTitle: "🔔 NOUVELLE COMMANDE - OUSSAMA WATCHES",
  
  // Paramètres de notification
  autoOpenWhatsApp: true, // Ouvre automatiquement WhatsApp
  includeDateTime: true,  // Inclut la date/heure dans le message
  includeStoreInfo: true, // Inclut les infos de la boutique
}

// Fonction utilitaire pour formater le numéro de téléphone
export const formatPhoneNumber = (phone: string): string => {
  // Supprime tous les espaces, tirets, parenthèses
  const cleaned = phone.replace(/[\s\-\(\)]/g, '')
  
  // Si le numéro commence par 0, remplace par 212 (Maroc)
  if (cleaned.startsWith('0')) {
    return '212' + cleaned.substring(1)
  }
  
  // Si le numéro commence déjà par 212, le retourne tel quel
  if (cleaned.startsWith('212')) {
    return cleaned
  }
  
  // Sinon, ajoute 212 par défaut
  return '212' + cleaned
}
