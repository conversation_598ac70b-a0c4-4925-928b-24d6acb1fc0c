import { prisma } from './prisma'
import type { FilterOptions } from '@/types'

// Products
export async function getProducts(filters?: FilterOptions) {
  try {
    const where: any = {
      isActive: true
    }

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { brand: { name: { contains: filters.search, mode: 'insensitive' } } }
      ]
    }

    if (filters?.category) {
      where.category = { name: filters.category }
    }

    if (filters?.brand) {
      where.brand = { name: filters.brand }
    }

    if (filters?.gender) {
      where.gender = filters.gender
    }

    if (filters?.minPrice) {
      where.price = { ...where.price, gte: filters.minPrice }
    }

    if (filters?.maxPrice) {
      where.price = { ...where.price, lte: filters.maxPrice }
    }

    const products = await prisma.product.findMany({
      where,
      include: {
        category: { select: { name: true } },
        brand: { select: { name: true } },
        images: {
          select: { imageUrl: true, altText: true, isPrimary: true },
          orderBy: { sortOrder: 'asc' }
        },
        reviews: { select: { rating: true } }
      }
    })

    return products
  } catch (error) {
    console.error('Error fetching products:', error)
    return []
  }
}

export async function getProductById(id: string) {
  try {
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: { select: { name: true } },
        brand: { select: { name: true } },
        images: {
          select: { imageUrl: true, altText: true, isPrimary: true, sortOrder: true },
          orderBy: { sortOrder: 'asc' }
        },
        reviews: {
          select: { 
            rating: true, 
            title: true, 
            comment: true, 
            createdAt: true,
            user: { select: { firstName: true } }
          },
          where: { isApproved: true }
        }
      }
    })

    return product
  } catch (error) {
    console.error('Error fetching product:', error)
    return null
  }
}

export async function getFeaturedProducts(limit = 6) {
  try {
    const products = await prisma.product.findMany({
      where: {
        isActive: true,
        isFeatured: true
      },
      include: {
        category: { select: { name: true } },
        brand: { select: { name: true } },
        images: {
          select: { imageUrl: true, altText: true, isPrimary: true },
          orderBy: { sortOrder: 'asc' }
        }
      },
      take: limit
    })

    return products
  } catch (error) {
    console.error('Error fetching featured products:', error)
    return []
  }
}

// Categories
export async function getCategories() {
  try {
    const categories = await prisma.category.findMany({
      orderBy: { name: 'asc' }
    })
    return categories
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

// Brands
export async function getBrands() {
  try {
    const brands = await prisma.brand.findMany({
      orderBy: { name: 'asc' }
    })
    return brands
  } catch (error) {
    console.error('Error fetching brands:', error)
    return []
  }
}

// Cart functions
export async function getCartItems(userId: string) {
  try {
    const cartItems = await prisma.cartItem.findMany({
      where: { userId },
      include: {
        product: {
          include: {
            brand: { select: { name: true } },
            images: {
              where: { isPrimary: true },
              select: { imageUrl: true }
            }
          }
        }
      }
    })
    return cartItems
  } catch (error) {
    console.error('Error fetching cart items:', error)
    return []
  }
}

export async function addToCart(userId: string, productId: string, quantity = 1) {
  try {
    await prisma.cartItem.upsert({
      where: {
        userId_productId: {
          userId,
          productId
        }
      },
      update: {
        quantity: { increment: quantity }
      },
      create: {
        userId,
        productId,
        quantity
      }
    })
    return true
  } catch (error) {
    console.error('Error adding to cart:', error)
    return false
  }
}

export async function updateCartItemQuantity(userId: string, productId: string, quantity: number) {
  try {
    if (quantity <= 0) {
      return removeFromCart(userId, productId)
    }

    await prisma.cartItem.update({
      where: {
        userId_productId: {
          userId,
          productId
        }
      },
      data: { quantity }
    })
    return true
  } catch (error) {
    console.error('Error updating cart item:', error)
    return false
  }
}

export async function removeFromCart(userId: string, productId: string) {
  try {
    await prisma.cartItem.delete({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    })
    return true
  } catch (error) {
    console.error('Error removing from cart:', error)
    return false
  }
}

export async function clearCart(userId: string) {
  try {
    await prisma.cartItem.deleteMany({
      where: { userId }
    })
    return true
  } catch (error) {
    console.error('Error clearing cart:', error)
    return false
  }
}

// Wishlist functions
export async function getWishlistItems(userId: string) {
  try {
    const wishlistItems = await prisma.wishlistItem.findMany({
      where: { userId },
      include: {
        product: {
          include: {
            brand: { select: { name: true } },
            images: {
              where: { isPrimary: true },
              select: { imageUrl: true }
            }
          }
        }
      }
    })
    return wishlistItems
  } catch (error) {
    console.error('Error fetching wishlist items:', error)
    return []
  }
}

export async function addToWishlist(userId: string, productId: string) {
  try {
    await prisma.wishlistItem.create({
      data: {
        userId,
        productId
      }
    })
    return true
  } catch (error) {
    console.error('Error adding to wishlist:', error)
    return false
  }
}

export async function removeFromWishlist(userId: string, productId: string) {
  try {
    await prisma.wishlistItem.delete({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    })
    return true
  } catch (error) {
    console.error('Error removing from wishlist:', error)
    return false
  }
}

// Contact messages
export async function createContactMessage(messageData: {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
}) {
  try {
    await prisma.contactMessage.create({
      data: messageData
    })
    return true
  } catch (error) {
    console.error('Error creating contact message:', error)
    return false
  }
}

// Discount codes
export async function validateDiscountCode(code: string, orderAmount: number) {
  try {
    const discountCode = await prisma.discountCode.findUnique({
      where: { code: code.toUpperCase() }
    })

    if (!discountCode || !discountCode.isActive) {
      return { valid: false, error: 'Code promo invalide' }
    }

    const now = new Date()
    
    if (discountCode.startsAt && discountCode.startsAt > now) {
      return { valid: false, error: 'Ce code promo n\'est pas encore actif' }
    }

    if (discountCode.expiresAt && discountCode.expiresAt < now) {
      return { valid: false, error: 'Ce code promo a expiré' }
    }

    if (discountCode.minimumOrderAmount && orderAmount < discountCode.minimumOrderAmount.toNumber()) {
      return { 
        valid: false, 
        error: `Commande minimum de ${discountCode.minimumOrderAmount}€ requise` 
      }
    }

    if (discountCode.usageLimit && discountCode.usedCount >= discountCode.usageLimit) {
      return { valid: false, error: 'Ce code promo a atteint sa limite d\'utilisation' }
    }

    let discountAmount = 0
    if (discountCode.type === 'PERCENTAGE') {
      discountAmount = (orderAmount * discountCode.value.toNumber()) / 100
      if (discountCode.maximumDiscountAmount) {
        discountAmount = Math.min(discountAmount, discountCode.maximumDiscountAmount.toNumber())
      }
    } else {
      discountAmount = discountCode.value.toNumber()
    }

    return {
      valid: true,
      discountAmount,
      code: discountCode
    }
  } catch (error) {
    console.error('Error validating discount code:', error)
    return { valid: false, error: 'Erreur lors de la validation du code' }
  }
}
