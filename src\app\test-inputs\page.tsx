'use client'

import { useState } from 'react'
import { Input, Textarea, Select } from '@/components/ui/Input'

export default function TestInputsPage() {
  const [formData, setFormData] = useState({
    text: 'Texte de test',
    email: '<EMAIL>',
    number: '123',
    textarea: 'Texte dans textarea',
    select: 'option1'
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Test des Inputs - Couleur Noire</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Inputs standards */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-6">Inputs Standards</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Input Text Standard
                </label>
                <input
                  type="text"
                  value={formData.text}
                  onChange={handleChange}
                  name="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent input-black-text"
                  placeholder="Tapez ici..."
                  style={{ color: '#000000' }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Input Email
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  name="email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent input-black-text"
                  placeholder="<EMAIL>"
                  style={{ color: '#000000' }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Input Number
                </label>
                <input
                  type="number"
                  value={formData.number}
                  onChange={handleChange}
                  name="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent input-black-text"
                  placeholder="123"
                  style={{ color: '#000000' }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Textarea Standard
                </label>
                <textarea
                  value={formData.textarea}
                  onChange={handleChange}
                  name="textarea"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent input-black-text"
                  placeholder="Tapez votre message..."
                  style={{ color: '#000000' }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Standard
                </label>
                <select
                  value={formData.select}
                  onChange={handleChange}
                  name="select"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent input-black-text"
                  style={{ color: '#000000' }}
                >
                  <option value="">Choisir une option</option>
                  <option value="option1">Option 1</option>
                  <option value="option2">Option 2</option>
                  <option value="option3">Option 3</option>
                </select>
              </div>
            </div>
          </div>

          {/* Composants personnalisés */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-6">Composants Personnalisés</h2>
            
            <div className="space-y-4">
              <Input
                label="Input Personnalisé"
                type="text"
                value={formData.text}
                onChange={handleChange}
                name="text"
                placeholder="Tapez ici..."
              />

              <Input
                label="Email Personnalisé"
                type="email"
                value={formData.email}
                onChange={handleChange}
                name="email"
                placeholder="<EMAIL>"
              />

              <Input
                label="Number Personnalisé"
                type="number"
                value={formData.number}
                onChange={handleChange}
                name="number"
                placeholder="123"
              />

              <Textarea
                label="Textarea Personnalisé"
                value={formData.textarea}
                onChange={handleChange}
                name="textarea"
                rows={3}
                placeholder="Tapez votre message..."
              />

              <Select
                label="Select Personnalisé"
                value={formData.select}
                onChange={handleChange}
                name="select"
              >
                <option value="">Choisir une option</option>
                <option value="option1">Option 1</option>
                <option value="option2">Option 2</option>
                <option value="option3">Option 3</option>
              </Select>
            </div>
          </div>
        </div>

        {/* Valeurs actuelles */}
        <div className="bg-white rounded-lg shadow p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4">Valeurs Actuelles</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm">
            {JSON.stringify(formData, null, 2)}
          </pre>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-semibold text-blue-800 mb-4">Instructions de Test</h2>
          <ul className="list-disc list-inside space-y-2 text-blue-700">
            <li>Le texte dans tous les inputs doit être <strong>NOIR</strong></li>
            <li>Les placeholders doivent être <strong>GRIS</strong></li>
            <li>Tapez dans les champs pour vérifier la couleur</li>
            <li>Testez le focus (cliquez dans un champ)</li>
            <li>Comparez les inputs standards vs personnalisés</li>
          </ul>
        </div>

        {/* Liens de navigation */}
        <div className="flex space-x-4 mt-8">
          <a
            href="/admin/products/new"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Tester Formulaire Admin
          </a>
          <a
            href="/montres"
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            Tester Catalogue
          </a>
          <a
            href="/"
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Retour Accueil
          </a>
        </div>
      </div>
    </div>
  )
}
