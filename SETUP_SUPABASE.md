# Configuration Supabase pour Oussama Watches

## 1. <PERSON><PERSON><PERSON> un projet Supabase

1. Allez sur [supabase.com](https://supabase.com)
2. Créez un compte ou connectez-vous
3. <PERSON><PERSON><PERSON> sur "New Project"
4. Choisissez votre organisation
5. Nommez votre projet "oussama-watches"
6. C<PERSON>ez un mot de passe sécurisé pour la base de données
7. Choisissez une région proche (Europe West par exemple)
8. C<PERSON>z sur "Create new project"

## 2. Configurer les variables d'environnement

1. Dans votre projet Supabase, allez dans Settings > API
2. Copiez les informations suivantes :
   - Project URL
   - anon public key
   - service_role key (gardez-la secrète !)

3. Met<PERSON>z à jour le fichier `.env.local` :

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=votre_project_url_ici
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre_anon_key_ici
SUPABASE_SERVICE_ROLE_KEY=votre_service_role_key_ici

# Stripe Configuration (à configurer plus tard)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Business Information
BUSINESS_NAME="Oussama Watches"
BUSINESS_EMAIL="<EMAIL>"
BUSINESS_PHONE="0603357867"
BUSINESS_ADDRESS="Casablanca - Sidi Bernoussi"
```

## 3. Exécuter les migrations

1. Dans votre projet Supabase, allez dans SQL Editor
2. Créez une nouvelle requête
3. Copiez le contenu du fichier `supabase/migrations/001_initial_schema.sql`
4. Exécutez la requête pour créer toutes les tables

5. Créez une nouvelle requête
6. Copiez le contenu du fichier `supabase/migrations/002_seed_data.sql`
7. Exécutez la requête pour insérer les données de démonstration

## 4. Configurer l'authentification

1. Dans votre projet Supabase, allez dans Authentication > Settings
2. Dans "Site URL", ajoutez : `http://localhost:3000`
3. Dans "Redirect URLs", ajoutez : `http://localhost:3000/auth/callback`

## 5. Configurer le stockage (optionnel)

1. Allez dans Storage
2. Créez un nouveau bucket appelé "product-images"
3. Rendez-le public pour les images de produits

## 6. Tester la configuration

1. Redémarrez votre serveur de développement :
```bash
npm run dev
```

2. Visitez http://localhost:3000
3. Essayez de :
   - Voir les produits dans le catalogue
   - Créer un compte
   - Ajouter des produits au panier
   - Utiliser les codes promo (WELCOME10, SAVE20)

## 7. Codes promo disponibles

- `WELCOME10` : 10% de réduction (commande min. 100€)
- `SAVE20` : 20% de réduction (commande min. 200€)
- `FIRST50` : 50€ de réduction (commande min. 300€)
- `LUXURY15` : 15% de réduction (commande min. 1000€)

## 8. Données de test

Le script de seed ajoute :
- 8 produits de démonstration
- 5 catégories (Luxe, Sport, Casual, Classique, Smartwatch)
- 8 marques (Rolex, Omega, TAG Heuer, Seiko, Casio, Citizen, Tissot, Apple)
- Avis clients
- Codes promo

## 9. Prochaines étapes

Une fois Supabase configuré, vous pourrez :
- Configurer Stripe pour les paiements
- Ajouter de vrais produits
- Personnaliser les catégories
- Configurer les emails de notification
- Déployer en production

## Dépannage

### Erreur de connexion à Supabase
- Vérifiez que les variables d'environnement sont correctes
- Redémarrez le serveur de développement
- Vérifiez que le projet Supabase est actif

### Erreurs de permissions
- Vérifiez que les politiques RLS sont correctement configurées
- Les utilisateurs non connectés peuvent voir les produits
- Les utilisateurs connectés peuvent gérer leur panier/wishlist

### Problèmes d'authentification
- Vérifiez les URLs de redirection dans Supabase
- Assurez-vous que l'email de confirmation est désactivé en développement
