const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    // Supprimer l'utilisateur de test s'il existe
    await prisma.user.deleteMany({
      where: {
        email: '<EMAIL>'
      }
    })

    // Créer un nouvel utilisateur de test
    const hashedPassword = await bcrypt.hash('123456', 12)

    const user = await prisma.user.create({
      data: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: hashedPassword,
        phone: '0123456789'
      }
    })

    console.log('✅ Utilisateur de test créé:')
    console.log(`📧 Email: <EMAIL>`)
    console.log(`🔑 Mot de passe: 123456`)
    console.log(`🆔 ID: ${user.id}`)

  } catch (error) {
    console.error('❌ Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
