import { NextRequest, NextResponse } from 'next/server'
import { prisma, isDatabaseConfigured } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    const filters = {
      search: searchParams.get('search') || undefined,
      category: searchParams.get('category') || undefined,
      brand: searchParams.get('brand') || undefined,
      gender: searchParams.get('gender') || undefined,
      minPrice: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : undefined
    }

    if (!isDatabaseConfigured) {
      // Retourner des données mockées si la base n'est pas configurée
      return NextResponse.json([])
    }

    // Construire la requête Prisma
    const whereClause: any = {
      isActive: true
    }

    if (filters.search) {
      whereClause.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
        { brand: { name: { contains: filters.search, mode: 'insensitive' } } }
      ]
    }

    if (filters.category) {
      whereClause.category = { name: filters.category }
    }

    if (filters.brand) {
      whereClause.brand = { name: filters.brand }
    }

    if (filters.gender) {
      whereClause.gender = filters.gender
    }

    if (filters.minPrice || filters.maxPrice) {
      whereClause.price = {}
      if (filters.minPrice) whereClause.price.gte = filters.minPrice
      if (filters.maxPrice) whereClause.price.lte = filters.maxPrice
    }

    const products = await prisma.product.findMany({
      where: whereClause,
      include: {
        category: { select: { name: true } },
        brand: { select: { name: true } },
        images: {
          select: {
            id: true,
            imageUrl: true,
            altText: true,
            isPrimary: true,
            sortOrder: true
          },
          orderBy: { sortOrder: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // Transformer les données pour correspondre au format attendu par le frontend
    const transformedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      original_price: product.originalPrice,
      category: { name: product.category?.name || '' },
      brand: { name: product.brand?.name || '' },
      gender: product.gender,
      stock_quantity: product.stockQuantity,
      sku: product.sku,
      features: product.features || [],
      is_featured: product.isFeatured,
      is_active: product.isActive,
      images: product.images.map(img => ({
        id: img.id,
        image_url: img.imageUrl,
        alt_text: img.altText,
        is_primary: img.isPrimary,
        sort_order: img.sortOrder
      })),
      reviews: [] // Pas de reviews pour l'instant
    }))

    console.log(`API /products: ${transformedProducts.length} produits trouvés`)
    return NextResponse.json(transformedProducts)

  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des produits' },
      { status: 500 }
    )
  }
}
