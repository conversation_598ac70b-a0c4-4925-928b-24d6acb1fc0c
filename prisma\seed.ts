import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Début du seeding...')

  // Créer les catégories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { name: 'Luxe' },
      update: {},
      create: {
        name: '<PERSON><PERSON>',
        description: 'Montres de luxe et prestige'
      }
    }),
    prisma.category.upsert({
      where: { name: 'Sport' },
      update: {},
      create: {
        name: 'Sport',
        description: 'Montres sportives et outdoor'
      }
    }),
    prisma.category.upsert({
      where: { name: 'Casual' },
      update: {},
      create: {
        name: 'Casual',
        description: 'Montres décontractées pour tous les jours'
      }
    }),
    prisma.category.upsert({
      where: { name: 'Classique' },
      update: {},
      create: {
        name: 'Classique',
        description: 'Montres classiques et élégantes'
      }
    }),
    prisma.category.upsert({
      where: { name: 'Smartwatch' },
      update: {},
      create: {
        name: 'Smartwatch',
        description: 'Montres connectées et intelligentes'
      }
    })
  ])

  console.log('✅ Catégories créées')

  // Créer les marques
  const brands = await Promise.all([
    prisma.brand.upsert({
      where: { name: 'Rolex' },
      update: {},
      create: {
        name: 'Rolex',
        description: 'Manufacture horlogère suisse de prestige',
        websiteUrl: 'https://www.rolex.com'
      }
    }),
    prisma.brand.upsert({
      where: { name: 'Omega' },
      update: {},
      create: {
        name: 'Omega',
        description: 'Horlogerie suisse de luxe',
        websiteUrl: 'https://www.omegawatches.com'
      }
    }),
    prisma.brand.upsert({
      where: { name: 'TAG Heuer' },
      update: {},
      create: {
        name: 'TAG Heuer',
        description: 'Horlogerie suisse sportive et luxe',
        websiteUrl: 'https://www.tagheuer.com'
      }
    }),
    prisma.brand.upsert({
      where: { name: 'Seiko' },
      update: {},
      create: {
        name: 'Seiko',
        description: 'Horlogerie japonaise innovante',
        websiteUrl: 'https://www.seiko.com'
      }
    }),
    prisma.brand.upsert({
      where: { name: 'Casio' },
      update: {},
      create: {
        name: 'Casio',
        description: 'Montres japonaises robustes et technologiques',
        websiteUrl: 'https://www.casio.com'
      }
    }),
    prisma.brand.upsert({
      where: { name: 'Citizen' },
      update: {},
      create: {
        name: 'Citizen',
        description: 'Horlogerie japonaise éco-responsable',
        websiteUrl: 'https://www.citizen.com'
      }
    }),
    prisma.brand.upsert({
      where: { name: 'Apple' },
      update: {},
      create: {
        name: 'Apple',
        description: 'Technologie et innovation',
        websiteUrl: 'https://www.apple.com'
      }
    })
  ])

  console.log('✅ Marques créées')

  // Créer les produits
  const products = [
    {
      name: 'Rolex Submariner Date',
      description: 'Montre de plongée iconique avec lunette tournante unidirectionnelle et étanchéité à 300 mètres.',
      price: 8500,
      originalPrice: 9000,
      categoryName: 'Luxe',
      brandName: 'Rolex',
      gender: 'homme' as const,
      stockQuantity: 5,
      sku: 'ROL-SUB-001',
      features: ['Étanche 300m', 'Mouvement automatique', 'Acier inoxydable', 'Lunette céramique'],
      isFeatured: true
    },
    {
      name: 'Omega Speedmaster Professional',
      description: 'La montre lunaire légendaire, chronographe mécanique de précision.',
      price: 3200,
      categoryName: 'Sport',
      brandName: 'Omega',
      gender: 'homme' as const,
      stockQuantity: 8,
      sku: 'OME-SPE-001',
      features: ['Chronographe', 'Mouvement manuel', 'Verre saphir', 'Fond transparent'],
      isFeatured: true
    },
    {
      name: 'TAG Heuer Formula 1',
      description: 'Montre sportive inspirée de la Formule 1, robuste et élégante.',
      price: 1200,
      categoryName: 'Sport',
      brandName: 'TAG Heuer',
      gender: 'homme' as const,
      stockQuantity: 12,
      sku: 'TAG-F1-001',
      features: ['Étanche 200m', 'Quartz', 'Lunette tournante', 'Bracelet acier'],
      isFeatured: false
    },
    {
      name: 'Casio G-Shock GA-2100',
      description: 'Montre ultra-résistante avec design moderne et fonctions multiples.',
      price: 120,
      categoryName: 'Sport',
      brandName: 'Casio',
      gender: 'homme' as const,
      stockQuantity: 25,
      sku: 'CAS-GSH-001',
      features: ['Résistant aux chocs', 'Étanche 200m', 'Éclairage LED', 'Alarme'],
      isFeatured: true
    },
    {
      name: 'Citizen Eco-Drive Titanium',
      description: 'Montre éco-responsable en titane avec mouvement à énergie lumineuse.',
      price: 280,
      categoryName: 'Casual',
      brandName: 'Citizen',
      gender: 'homme' as const,
      stockQuantity: 15,
      sku: 'CIT-ECO-001',
      features: ['Énergie lumineuse', 'Titane', 'Étanche 100m', 'Date'],
      isFeatured: false
    },
    {
      name: 'Apple Watch Series 9',
      description: 'Montre connectée avec GPS, santé et fitness avancés.',
      price: 450,
      categoryName: 'Smartwatch',
      brandName: 'Apple',
      gender: 'unisexe' as const,
      stockQuantity: 20,
      sku: 'APP-WS9-001',
      features: ['GPS', 'Moniteur cardiaque', 'Étanche 50m', 'Écran Retina'],
      isFeatured: true
    }
  ]

  for (const productData of products) {
    const category = categories.find(c => c.name === productData.categoryName)
    const brand = brands.find(b => b.name === productData.brandName)

    if (category && brand) {
      const product = await prisma.product.upsert({
        where: { sku: productData.sku },
        update: {},
        create: {
          name: productData.name,
          description: productData.description,
          price: productData.price,
          originalPrice: productData.originalPrice,
          categoryId: category.id,
          brandId: brand.id,
          gender: productData.gender,
          stockQuantity: productData.stockQuantity,
          sku: productData.sku,
          features: productData.features,
          isFeatured: productData.isFeatured
        }
      })

      // Ajouter une image par défaut
      const existingImage = await prisma.productImage.findFirst({
        where: {
          productId: product.id,
          isPrimary: true
        }
      })

      if (!existingImage) {
        await prisma.productImage.create({
          data: {
            productId: product.id,
            imageUrl: `/images/products/${productData.sku.toLowerCase()}.jpg`,
            altText: productData.name,
            isPrimary: true,
            sortOrder: 0
          }
        })
      }
    }
  }

  console.log('✅ Produits créés')

  // Créer les codes de réduction
  await Promise.all([
    prisma.discountCode.upsert({
      where: { code: 'WELCOME10' },
      update: {},
      create: {
        code: 'WELCOME10',
        description: 'Code de bienvenue - 10% de réduction',
        type: 'PERCENTAGE',
        value: 10,
        minimumOrderAmount: 100,
        usageLimit: 100,
        isActive: true,
        expiresAt: new Date('2024-12-31')
      }
    }),
    prisma.discountCode.upsert({
      where: { code: 'SAVE20' },
      update: {},
      create: {
        code: 'SAVE20',
        description: 'Réduction spéciale - 20% de réduction',
        type: 'PERCENTAGE',
        value: 20,
        minimumOrderAmount: 200,
        usageLimit: 50,
        isActive: true,
        expiresAt: new Date('2024-12-31')
      }
    }),
    prisma.discountCode.upsert({
      where: { code: 'FIRST50' },
      update: {},
      create: {
        code: 'FIRST50',
        description: 'Première commande - 50€ de réduction',
        type: 'FIXED_AMOUNT',
        value: 50,
        minimumOrderAmount: 300,
        usageLimit: 200,
        isActive: true,
        expiresAt: new Date('2024-12-31')
      }
    })
  ])

  console.log('✅ Codes de réduction créés')

  // Créer un utilisateur de test
  const hashedPassword = await bcrypt.hash('password123', 10)
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Test',
      lastName: 'User'
    }
  })

  console.log('✅ Utilisateur de test créé (<EMAIL> / password123)')
  console.log('🎉 Seeding terminé avec succès!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
