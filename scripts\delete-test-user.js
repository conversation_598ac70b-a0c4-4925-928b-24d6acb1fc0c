const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function deleteTestUser() {
  try {
    // Supprimer le compte de test
    const deletedUser = await prisma.user.delete({
      where: {
        email: '<EMAIL>'
      }
    })

    console.log('✅ Compte de test supprimé avec succès')
    console.log(`📧 Email supprimé: ${deletedUser.email}`)

  } catch (error) {
    if (error.code === 'P2025') {
      console.log('ℹ️ Aucun compte de test trouvé à supprimer')
    } else {
      console.error('❌ Erreur lors de la suppression:', error)
    }
  } finally {
    await prisma.$disconnect()
  }
}

deleteTestUser()
