const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function addProductImages() {
  try {
    // Récupérer tous les produits
    const products = await prisma.product.findMany({
      include: {
        images: true
      }
    })

    console.log(`📦 ${products.length} produits trouvés`)

    // Images de montres par défaut
    const watchImages = [
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1533139502658-0198f920d8e8?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1508057198894-247b23fe5ade?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1609081219090-a6d81d3085bf?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=400&h=400&fit=crop'
    ]

    for (let i = 0; i < products.length; i++) {
      const product = products[i]
      
      // Si le produit n'a pas d'images, en ajouter
      if (product.images.length === 0) {
        const imageUrl = watchImages[i % watchImages.length]
        
        await prisma.productImage.create({
          data: {
            productId: product.id,
            url: imageUrl,
            alt: `Image de ${product.name}`,
            isPrimary: true
          }
        })
        
        console.log(`✅ Image ajoutée pour: ${product.name}`)
      } else {
        console.log(`ℹ️ ${product.name} a déjà ${product.images.length} image(s)`)
      }
    }

    console.log('🎉 Toutes les images ont été ajoutées !')

  } catch (error) {
    console.error('❌ Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addProductImages()
