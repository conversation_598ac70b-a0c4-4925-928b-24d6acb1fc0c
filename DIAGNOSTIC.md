# 🔍 Diagnostic des erreurs "undefined"

## Étapes de diagnostic

### 1. Vérifier la console du navigateur
1. Ouvrez les outils de développement (F12)
2. Allez dans l'onglet "Console"
3. Recherchez les erreurs en rouge
4. Notez les messages d'erreur complets

### 2. Tester les API individuellement
Allez sur : http://localhost:3000/test-admin

Cette page teste :
- ✅ API Catégories : `/api/admin/categories`
- ✅ API Marques : `/api/admin/brands`  
- ✅ API Produits : `/api/products`

### 3. Vérifier la base de données
```bash
# Dans phpMyAdmin, vérifiez que ces tables existent :
- Category
- Brand  
- Product
- ProductImage
- User
```

### 4. Vérifier les variables d'environnement
Fichier `.env.local` doit contenir :
```
DATABASE_URL="mysql://root@localhost:3306/oussama_watches"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

### 5. Erreurs communes et solutions

#### Erreur : "Cannot read property 'id' of undefined"
**Cause :** L'objet produit n'est pas retourné correctement
**Solution :** Vérifier l'API `/api/admin/products`

#### Erreur : "FormData is not defined"
**Cause :** Problème côté serveur avec l'upload
**Solution :** Vérifier l'API `/api/admin/upload-image`

#### Erreur : "fetch is not defined"
**Cause :** Problème de réseau ou URL incorrecte
**Solution :** Vérifier que le serveur est démarré

#### Erreur : "Prisma client not found"
**Cause :** Base de données non configurée
**Solution :** Exécuter `npx prisma generate`

### 6. Tests manuels

#### Test 1 : Création produit sans images
1. Allez sur `/admin/products/new`
2. Remplissez seulement les champs obligatoires
3. Ne sélectionnez pas d'images
4. Cliquez sur "Créer le produit"

#### Test 2 : Test API direct
```bash
# Test avec curl (dans PowerShell)
Invoke-WebRequest -Uri "http://localhost:3000/api/admin/categories" -Method GET
```

#### Test 3 : Vérifier les logs serveur
Regardez la console où vous avez lancé `npm run dev`

### 7. Solutions rapides

#### Redémarrer tout :
```bash
# Arrêter le serveur (Ctrl+C)
# Puis relancer :
npm run dev
```

#### Régénérer Prisma :
```bash
npx prisma generate
npx prisma db push
```

#### Vider le cache :
```bash
# Dans le navigateur : Ctrl+Shift+R (rechargement forcé)
# Ou vider le cache dans les outils de développement
```

### 8. Informations à collecter

Si l'erreur persiste, collectez :
1. **Message d'erreur exact** de la console
2. **URL** où l'erreur se produit
3. **Étapes** pour reproduire l'erreur
4. **Résultat** de la page de test `/test-admin`
5. **État** de XAMPP (Apache + MySQL démarrés ?)

### 9. Vérifications système

#### XAMPP :
- [ ] Apache démarré
- [ ] MySQL démarré  
- [ ] Base `oussama_watches` existe
- [ ] Tables créées

#### Next.js :
- [ ] Serveur démarré sur port 3000
- [ ] Pas d'erreurs au démarrage
- [ ] Variables d'environnement chargées

#### Navigateur :
- [ ] JavaScript activé
- [ ] Pas de bloqueur de publicité agressif
- [ ] Cache vidé

---

**Commencez par la page de test : http://localhost:3000/test-admin**
