'use client'

import { useState, useEffect } from 'react'
import { useAuth } from './useAuth'
import { isDatabaseConfigured } from '@/lib/prisma'
import {
  getWishlistItems,
  addToWishlist as addToWishlistDB,
  removeFromWishlist as removeFromWishlistDB
} from '@/lib/database'

export function useWishlist() {
  const { user } = useAuth()
  const [wishlistItems, setWishlistItems] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (user) {
      loadWishlistItems()
    } else {
      setWishlistItems([])
    }
  }, [user])

  const loadWishlistItems = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const items = await getWishlistItems(user.id)
      setWishlistItems(items)
    } catch (error) {
      console.error('Error loading wishlist items:', error)
    } finally {
      setLoading(false)
    }
  }

  const addToWishlist = async (productId: string) => {
    if (!user) {
      // Handle guest wishlist (localStorage)
      const guestWishlist = JSON.parse(localStorage.getItem('guestWishlist') || '[]')
      if (!guestWishlist.includes(productId)) {
        guestWishlist.push(productId)
        localStorage.setItem('guestWishlist', JSON.stringify(guestWishlist))
      }
      return true
    }

    const success = await addToWishlistDB(user.id, productId)
    if (success) {
      await loadWishlistItems()
    }
    return success
  }

  const removeFromWishlist = async (productId: string) => {
    if (!user) {
      const guestWishlist = JSON.parse(localStorage.getItem('guestWishlist') || '[]')
      const filteredWishlist = guestWishlist.filter((id: string) => id !== productId)
      localStorage.setItem('guestWishlist', JSON.stringify(filteredWishlist))
      return true
    }

    const success = await removeFromWishlistDB(user.id, productId)
    if (success) {
      await loadWishlistItems()
    }
    return success
  }

  const isInWishlist = (productId: string) => {
    if (!user) {
      const guestWishlist = JSON.parse(localStorage.getItem('guestWishlist') || '[]')
      return guestWishlist.includes(productId)
    }
    
    return wishlistItems.some(item => item.product_id === productId)
  }

  const toggleWishlist = async (productId: string) => {
    if (isInWishlist(productId)) {
      return await removeFromWishlist(productId)
    } else {
      return await addToWishlist(productId)
    }
  }

  return {
    wishlistItems,
    loading,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    toggleWishlist,
    refreshWishlist: loadWishlistItems
  }
}
