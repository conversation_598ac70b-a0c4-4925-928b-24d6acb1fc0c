-- Script SQL pour configurer la base de données Oussama Watches
USE oussama_watches;

-- <PERSON><PERSON><PERSON> les tables si elles n'existent pas
CREATE TABLE IF NOT EXISTS `Category` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `image_url` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `Category_name_key`(`name`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `Brand` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `logo_url` VARCHAR(191) NULL,
    `website_url` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `Brand_name_key`(`name`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `Product` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `price` DECIMAL(10, 2) NOT NULL,
    `original_price` DECIMAL(10, 2) NULL,
    `category_id` VARCHAR(191) NOT NULL,
    `brand_id` VARCHAR(191) NOT NULL,
    `gender` ENUM('homme', 'femme', 'enfant', 'unisexe') NOT NULL,
    `stock_quantity` INTEGER NOT NULL DEFAULT 0,
    `min_stock_level` INTEGER NOT NULL DEFAULT 5,
    `sku` VARCHAR(191) NOT NULL,
    `weight` DECIMAL(8, 2) NULL,
    `features` JSON NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `is_featured` BOOLEAN NOT NULL DEFAULT false,
    `meta_title` VARCHAR(191) NULL,
    `meta_description` TEXT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `Product_sku_key`(`sku`),
    INDEX `Product_category_id_fkey`(`category_id`),
    INDEX `Product_brand_id_fkey`(`brand_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `ProductImage` (
    `id` VARCHAR(191) NOT NULL,
    `product_id` VARCHAR(191) NOT NULL,
    `image_url` VARCHAR(191) NOT NULL,
    `alt_text` VARCHAR(191) NULL,
    `sort_order` INTEGER NOT NULL DEFAULT 0,
    `is_primary` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`),
    INDEX `ProductImage_product_id_fkey`(`product_id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `User` (
    `id` VARCHAR(191) NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `password` VARCHAR(191) NOT NULL,
    `first_name` VARCHAR(191) NULL,
    `last_name` VARCHAR(191) NULL,
    `phone` VARCHAR(191) NULL,
    `date_of_birth` DATETIME(3) NULL,
    `gender` VARCHAR(191) NULL,
    `avatar_url` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `User_email_key`(`email`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `DiscountCode` (
    `id` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `type` ENUM('PERCENTAGE', 'FIXED_AMOUNT') NOT NULL,
    `value` DECIMAL(10, 2) NOT NULL,
    `minimum_order_amount` DECIMAL(10, 2) NULL,
    `maximum_discount_amount` DECIMAL(10, 2) NULL,
    `usage_limit` INTEGER NULL,
    `used_count` INTEGER NOT NULL DEFAULT 0,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `starts_at` DATETIME(3) NULL,
    `expires_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `DiscountCode_code_key`(`code`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Insérer les catégories
INSERT IGNORE INTO `Category` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES
('cat1', 'Luxe', 'Montres de luxe et prestige', NOW(), NOW()),
('cat2', 'Sport', 'Montres sportives et outdoor', NOW(), NOW()),
('cat3', 'Casual', 'Montres décontractées pour tous les jours', NOW(), NOW()),
('cat4', 'Classique', 'Montres classiques et élégantes', NOW(), NOW()),
('cat5', 'Smartwatch', 'Montres connectées et intelligentes', NOW(), NOW());

-- Insérer les marques
INSERT IGNORE INTO `Brand` (`id`, `name`, `description`, `website_url`, `created_at`, `updated_at`) VALUES
('brand1', 'Rolex', 'Manufacture horlogère suisse de prestige', 'https://www.rolex.com', NOW(), NOW()),
('brand2', 'Omega', 'Horlogerie suisse de luxe', 'https://www.omegawatches.com', NOW(), NOW()),
('brand3', 'TAG Heuer', 'Horlogerie suisse sportive et luxe', 'https://www.tagheuer.com', NOW(), NOW()),
('brand4', 'Seiko', 'Horlogerie japonaise innovante', 'https://www.seiko.com', NOW(), NOW()),
('brand5', 'Casio', 'Montres japonaises robustes et technologiques', 'https://www.casio.com', NOW(), NOW()),
('brand6', 'Citizen', 'Horlogerie japonaise éco-responsable', 'https://www.citizen.com', NOW(), NOW()),
('brand7', 'Apple', 'Technologie et innovation', 'https://www.apple.com', NOW(), NOW());

-- Insérer les produits
INSERT IGNORE INTO `Product` (`id`, `name`, `description`, `price`, `original_price`, `category_id`, `brand_id`, `gender`, `stock_quantity`, `sku`, `features`, `is_featured`, `created_at`, `updated_at`) VALUES
('prod1', 'Rolex Submariner Date', 'Montre de plongée iconique avec lunette tournante unidirectionnelle et étanchéité à 300 mètres.', 8500.00, 9000.00, 'cat1', 'brand1', 'homme', 5, 'ROL-SUB-001', '["Étanche 300m", "Mouvement automatique", "Acier inoxydable", "Lunette céramique"]', true, NOW(), NOW()),
('prod2', 'Omega Speedmaster Professional', 'La montre lunaire légendaire, chronographe mécanique de précision.', 3200.00, NULL, 'cat2', 'brand2', 'homme', 8, 'OME-SPE-001', '["Chronographe", "Mouvement manuel", "Verre saphir", "Fond transparent"]', true, NOW(), NOW()),
('prod3', 'TAG Heuer Formula 1', 'Montre sportive inspirée de la Formule 1, robuste et élégante.', 1200.00, NULL, 'cat2', 'brand3', 'homme', 12, 'TAG-F1-001', '["Étanche 200m", "Quartz", "Lunette tournante", "Bracelet acier"]', false, NOW(), NOW()),
('prod4', 'Casio G-Shock GA-2100', 'Montre ultra-résistante avec design moderne et fonctions multiples.', 120.00, NULL, 'cat2', 'brand5', 'homme', 25, 'CAS-GSH-001', '["Résistant aux chocs", "Étanche 200m", "Éclairage LED", "Alarme"]', true, NOW(), NOW()),
('prod5', 'Citizen Eco-Drive Titanium', 'Montre éco-responsable en titane avec mouvement à énergie lumineuse.', 280.00, NULL, 'cat3', 'brand6', 'homme', 15, 'CIT-ECO-001', '["Énergie lumineuse", "Titane", "Étanche 100m", "Date"]', false, NOW(), NOW()),
('prod6', 'Apple Watch Series 9', 'Montre connectée avec GPS, santé et fitness avancés.', 450.00, NULL, 'cat5', 'brand7', 'unisexe', 20, 'APP-WS9-001', '["GPS", "Moniteur cardiaque", "Étanche 50m", "Écran Retina"]', true, NOW(), NOW());

-- Insérer les images des produits
INSERT IGNORE INTO `ProductImage` (`id`, `product_id`, `image_url`, `alt_text`, `is_primary`, `sort_order`, `created_at`) VALUES
('img1', 'prod1', '/images/products/rol-sub-001.jpg', 'Rolex Submariner Date', true, 0, NOW()),
('img2', 'prod2', '/images/products/ome-spe-001.jpg', 'Omega Speedmaster Professional', true, 0, NOW()),
('img3', 'prod3', '/images/products/tag-f1-001.jpg', 'TAG Heuer Formula 1', true, 0, NOW()),
('img4', 'prod4', '/images/products/cas-gsh-001.jpg', 'Casio G-Shock GA-2100', true, 0, NOW()),
('img5', 'prod5', '/images/products/cit-eco-001.jpg', 'Citizen Eco-Drive Titanium', true, 0, NOW()),
('img6', 'prod6', '/images/products/app-ws9-001.jpg', 'Apple Watch Series 9', true, 0, NOW());

-- Insérer les codes de réduction
INSERT IGNORE INTO `DiscountCode` (`id`, `code`, `description`, `type`, `value`, `minimum_order_amount`, `usage_limit`, `is_active`, `expires_at`, `created_at`, `updated_at`) VALUES
('disc1', 'WELCOME10', 'Code de bienvenue - 10% de réduction', 'PERCENTAGE', 10.00, 100.00, 100, true, '2024-12-31 23:59:59', NOW(), NOW()),
('disc2', 'SAVE20', 'Réduction spéciale - 20% de réduction', 'PERCENTAGE', 20.00, 200.00, 50, true, '2024-12-31 23:59:59', NOW(), NOW()),
('disc3', 'FIRST50', 'Première commande - 50€ de réduction', 'FIXED_AMOUNT', 50.00, 300.00, 200, true, '2024-12-31 23:59:59', NOW(), NOW());

-- Insérer un utilisateur de test (mot de passe: password123)
INSERT IGNORE INTO `User` (`id`, `email`, `password`, `first_name`, `last_name`, `created_at`, `updated_at`) VALUES
('user1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'User', NOW(), NOW());

-- Ajouter les contraintes de clés étrangères
ALTER TABLE `Product` ADD CONSTRAINT `Product_category_id_fkey` FOREIGN KEY (`category_id`) REFERENCES `Category`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `Product` ADD CONSTRAINT `Product_brand_id_fkey` FOREIGN KEY (`brand_id`) REFERENCES `Brand`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `ProductImage` ADD CONSTRAINT `ProductImage_product_id_fkey` FOREIGN KEY (`product_id`) REFERENCES `Product`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
