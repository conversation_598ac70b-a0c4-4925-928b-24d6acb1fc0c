'use client'

import { useState, useEffect } from 'react'
import { useAuth } from './useAuth'
import { isDatabaseConfigured } from '@/lib/prisma'
import {
  getCartItems,
  addToCart as addToCartDB,
  updateCartItemQuantity as updateCartItemQuantityDB,
  removeFromCart as removeFromCartDB,
  clearCart as clearCartDB
} from '@/lib/database'

export function useCart() {
  const { user } = useAuth()
  const [cartItems, setCartItems] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (user) {
      loadCartItems()
    } else {
      setCartItems([])
    }
  }, [user])

  const loadCartItems = async () => {
    if (!user || !isDatabaseConfigured) return

    setLoading(true)
    try {
      const items = await getCartItems(user.id)
      setCartItems(items)
    } catch (error) {
      console.error('Error loading cart items:', error)
    } finally {
      setLoading(false)
    }
  }

  const addToCart = async (productId: string, quantity = 1) => {
    if (!user) {
      // Handle guest cart (localStorage)
      const guestCart = JSON.parse(localStorage.getItem('guestCart') || '[]')
      const existingItem = guestCart.find((item: any) => item.productId === productId)
      
      if (existingItem) {
        existingItem.quantity += quantity
      } else {
        guestCart.push({ productId, quantity })
      }
      
      localStorage.setItem('guestCart', JSON.stringify(guestCart))
      return true
    }

    const success = await addToCartDB(user.id, productId, quantity)
    if (success) {
      await loadCartItems()
    }
    return success
  }

  const updateQuantity = async (productId: string, quantity: number) => {
    if (!user) {
      const guestCart = JSON.parse(localStorage.getItem('guestCart') || '[]')
      const itemIndex = guestCart.findIndex((item: any) => item.productId === productId)
      
      if (itemIndex >= 0) {
        if (quantity <= 0) {
          guestCart.splice(itemIndex, 1)
        } else {
          guestCart[itemIndex].quantity = quantity
        }
        localStorage.setItem('guestCart', JSON.stringify(guestCart))
      }
      return true
    }

    const success = await updateCartItemQuantityDB(user.id, productId, quantity)
    if (success) {
      await loadCartItems()
    }
    return success
  }

  const removeItem = async (productId: string) => {
    if (!user) {
      const guestCart = JSON.parse(localStorage.getItem('guestCart') || '[]')
      const filteredCart = guestCart.filter((item: any) => item.productId !== productId)
      localStorage.setItem('guestCart', JSON.stringify(filteredCart))
      return true
    }

    const success = await removeFromCartDB(user.id, productId)
    if (success) {
      await loadCartItems()
    }
    return success
  }

  const clearCart = async () => {
    if (!user) {
      localStorage.removeItem('guestCart')
      return true
    }

    const success = await clearCartDB(user.id)
    if (success) {
      await loadCartItems()
    }
    return success
  }

  const getCartTotal = () => {
    return cartItems.reduce((total, item) => {
      return total + (item.product.price * item.quantity)
    }, 0)
  }

  const getCartItemsCount = () => {
    return cartItems.reduce((count, item) => count + item.quantity, 0)
  }

  return {
    cartItems,
    loading,
    addToCart,
    updateQuantity,
    removeItem,
    clearCart,
    getCartTotal,
    getCartItemsCount,
    refreshCart: loadCartItems
  }
}
