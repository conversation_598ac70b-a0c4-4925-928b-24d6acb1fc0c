'use client'

import { useState, useEffect } from 'react'
import { getCurrentUser } from '@/lib/simpleAuth'

export function useCart() {
  const [cartItems, setCartItems] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    // Charger l'utilisateur et écouter les changements
    const currentUser = getCurrentUser()
    setUser(currentUser)

    if (currentUser) {
      loadCartItems(currentUser.id)
    } else {
      setCartItems([])
    }

    const handleUserChange = (event: any) => {
      const newUser = event.detail
      setUser(newUser)
      if (newUser) {
        loadCartItems(newUser.id)
      } else {
        setCartItems([])
      }
    }

    window.addEventListener('userChanged', handleUserChange)
    return () => window.removeEventListener('userChanged', handleUserChange)
  }, [])

  const loadCartItems = async (userId: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/cart?userId=${userId}`)
      const data = await response.json()

      if (response.ok) {
        setCartItems(data.cartItems || [])
      } else {
        console.error('Erreur lors du chargement du panier:', data.error)
      }
    } catch (error) {
      console.error('Erreur lors du chargement du panier:', error)
    } finally {
      setLoading(false)
    }
  }

  const addToCart = async (productId: string, quantity = 1) => {
    if (!user) {
      alert('Vous devez être connecté pour ajouter des produits au panier')
      return false
    }

    setLoading(true)
    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id, productId, quantity })
      })

      const data = await response.json()

      if (response.ok) {
        // Recharger le panier
        await loadCartItems(user.id)
        return true
      } else {
        console.error('Erreur lors de l\'ajout au panier:', data.error)
        return false
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout au panier:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  const updateQuantity = async (productId: string, quantity: number) => {
    if (!user) {
      const guestCart = JSON.parse(localStorage.getItem('guestCart') || '[]')
      const itemIndex = guestCart.findIndex((item: any) => item.productId === productId)
      
      if (itemIndex >= 0) {
        if (quantity <= 0) {
          guestCart.splice(itemIndex, 1)
        } else {
          guestCart[itemIndex].quantity = quantity
        }
        localStorage.setItem('guestCart', JSON.stringify(guestCart))
      }
      return true
    }

    const success = await updateCartItemQuantityDB(user.id, productId, quantity)
    if (success) {
      await loadCartItems()
    }
    return success
  }

  const removeItem = async (productId: string) => {
    if (!user) {
      const guestCart = JSON.parse(localStorage.getItem('guestCart') || '[]')
      const filteredCart = guestCart.filter((item: any) => item.productId !== productId)
      localStorage.setItem('guestCart', JSON.stringify(filteredCart))
      return true
    }

    const success = await removeFromCartDB(user.id, productId)
    if (success) {
      await loadCartItems()
    }
    return success
  }

  const clearCart = async () => {
    if (!user) {
      localStorage.removeItem('guestCart')
      return true
    }

    const success = await clearCartDB(user.id)
    if (success) {
      await loadCartItems()
    }
    return success
  }

  const getCartTotal = () => {
    return cartItems.reduce((total, item) => {
      return total + (item.product.price * item.quantity)
    }, 0)
  }

  const getCartItemsCount = () => {
    return cartItems.reduce((count, item) => count + item.quantity, 0)
  }

  return {
    cartItems,
    loading,
    addToCart,
    updateQuantity,
    removeItem,
    clearCart,
    getCartTotal,
    getCartItemsCount,
    refreshCart: loadCartItems
  }
}
