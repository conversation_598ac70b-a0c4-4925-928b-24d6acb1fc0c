import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { prisma, isDatabaseConfigured } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    if (!isDatabaseConfigured) {
      return NextResponse.json(
        { error: 'Base de données non configurée' },
        { status: 500 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('image') as File
    const productId = formData.get('productId') as string
    const isPrimary = formData.get('isPrimary') === 'true'
    const sortOrder = parseInt(formData.get('sortOrder') as string) || 0

    console.log('Upload request:', {
      fileName: file?.name,
      fileSize: file?.size,
      productId,
      isPrimary,
      sortOrder
    })

    if (!file) {
      return NextResponse.json(
        { error: 'Aucun fichier fourni' },
        { status: 400 }
      )
    }

    if (!productId) {
      return NextResponse.json(
        { error: 'ID du produit requis' },
        { status: 400 }
      )
    }

    // Vérifier le type de fichier
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Le fichier doit être une image' },
        { status: 400 }
      )
    }

    // Vérifier la taille (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'L\'image ne doit pas dépasser 5MB' },
        { status: 400 }
      )
    }

    // Créer le nom de fichier unique
    const timestamp = Date.now()
    const extension = file.name.split('.').pop()
    const fileName = `${productId}-${timestamp}.${extension}`

    // Créer le dossier s'il n'existe pas
    const uploadDir = join(process.cwd(), 'public', 'images', 'products')
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // Le dossier existe déjà
    }

    // Sauvegarder le fichier
    const filePath = join(uploadDir, fileName)
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // URL publique de l'image
    const imageUrl = `/images/products/${fileName}`

    // Vérifier que le produit existe
    const product = await prisma.product.findUnique({
      where: { id: productId }
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Produit non trouvé' },
        { status: 404 }
      )
    }

    // Sauvegarder en base de données
    const productImage = await prisma.productImage.create({
      data: {
        productId,
        imageUrl,
        altText: file.name,
        isPrimary,
        sortOrder
      }
    })

    console.log('Image créée en BDD:', productImage)

    return NextResponse.json({
      message: 'Image uploadée avec succès',
      imageUrl,
      imageId: productImage.id
    })

  } catch (error) {
    console.error('Error uploading image:', error)
    return NextResponse.json(
      { error: 'Erreur lors de l\'upload de l\'image' },
      { status: 500 }
    )
  }
}
