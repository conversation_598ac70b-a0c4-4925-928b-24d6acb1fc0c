'use client'

import { useState, useEffect } from 'react'

export default function TestImagesPage() {
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = async () => {
    try {
      const response = await fetch('/api/admin/products')
      const data = await response.json()
      setProducts(data)
      console.log('Produits avec images:', data)
    } catch (error) {
      console.error('Erreur:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <div className="p-8">Chargement...</div>
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Test Images Produits</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <div key={product.id} className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">{product.name}</h2>
              
              <div className="mb-4">
                <p><strong>Prix:</strong> {product.price}€</p>
                <p><strong>Stock:</strong> {product.stockQuantity}</p>
                <p><strong>Catégorie:</strong> {product.category?.name}</p>
                <p><strong>Marque:</strong> {product.brand?.name}</p>
              </div>

              <div className="mb-4">
                <h3 className="font-semibold mb-2">Images ({product.images?.length || 0}):</h3>
                
                {product.images && product.images.length > 0 ? (
                  <div className="space-y-2">
                    {product.images.map((image: any, index: number) => (
                      <div key={image.id} className="border rounded p-2">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">
                            Image {index + 1} {image.isPrimary && '(Principale)'}
                          </span>
                          <span className="text-xs text-gray-500">
                            Ordre: {image.sortOrder}
                          </span>
                        </div>
                        
                        <div className="mb-2">
                          <img
                            src={image.imageUrl}
                            alt={image.altText}
                            className="w-full h-32 object-cover rounded border"
                            onError={(e) => {
                              console.error('Erreur chargement image:', image.imageUrl)
                              e.currentTarget.style.display = 'none'
                            }}
                          />
                        </div>
                        
                        <div className="text-xs text-gray-600">
                          <p><strong>URL:</strong> {image.imageUrl}</p>
                          <p><strong>Alt:</strong> {image.altText}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">Aucune image</p>
                )}
              </div>

              <div className="text-xs text-gray-500">
                <p><strong>ID:</strong> {product.id}</p>
                <p><strong>SKU:</strong> {product.sku}</p>
                <p><strong>Créé:</strong> {new Date(product.createdAt).toLocaleString()}</p>
              </div>
            </div>
          ))}
        </div>

        {products.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">Aucun produit trouvé</p>
          </div>
        )}

        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Actions de test</h2>
          <div className="space-x-4">
            <button
              onClick={loadProducts}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Recharger les produits
            </button>
            
            <button
              onClick={() => window.location.href = '/admin/products/new'}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Ajouter un produit
            </button>
            
            <button
              onClick={() => window.location.href = '/montres'}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
            >
              Voir le catalogue
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
