'use client'

import { useState } from 'react'
import { isSupabaseConfigured } from '@/lib/supabase'
import { X, Info, Database, Settings } from 'lucide-react'

export default function DemoNotice() {
  const [isVisible, setIsVisible] = useState(!isSupabaseConfigured)

  if (!isVisible || isSupabaseConfigured) {
    return null
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div className="flex">
        <div className="flex-shrink-0">
          <Info className="h-5 w-5 text-blue-400" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-blue-800">
            Bienvenue dans la démonstration d'Oussama Watches !
          </h3>
          <div className="mt-2 text-sm text-blue-700">
            <p className="mb-2">
              Cette boutique fonctionne actuellement en mode démonstration avec des données d'exemple.
            </p>
            <div className="space-y-1">
              <p>✅ Navigation et interface utilisateur</p>
              <p>✅ Catalogue de produits avec filtres</p>
              <p>✅ Panier d'achat (stockage local)</p>
              <p>✅ Pages de contact et informations</p>
            </div>
            <div className="mt-3 p-3 bg-blue-100 rounded border border-blue-200">
              <div className="flex items-center mb-2">
                <Database className="h-4 w-4 text-blue-600 mr-2" />
                <span className="font-medium text-blue-800">Pour activer toutes les fonctionnalités :</span>
              </div>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Configurez Supabase (base de données)</li>
                <li>Configurez Stripe (paiements)</li>
                <li>Ajoutez vos vrais produits</li>
              </ol>
              <div className="mt-2">
                <a
                  href="https://github.com/your-repo/setup-guide"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  <Settings className="h-3 w-3 mr-1" />
                  Guide de configuration complet
                </a>
              </div>
            </div>
          </div>
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={() => setIsVisible(false)}
            className="bg-blue-50 rounded-md p-1.5 text-blue-400 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}
