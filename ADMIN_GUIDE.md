# Guide d'Administration - Oussama Watches

## 🔐 Accès à l'administration

### Comment accéder à l'admin :
1. **Connectez-vous** avec votre compte utilisateur
2. **Cliquez sur l'icône engrenage** (⚙️) dans le header
3. Ou allez directement sur : http://localhost:3000/admin

### Compte de test :
- **Email** : <EMAIL>
- **Mot de passe** : password123

## 📊 Tableau de bord

Le tableau de bord vous donne un aperçu rapide de votre boutique :
- **Nombre de produits** en stock
- **Nombre d'utilisateurs** inscrits
- **Nombre de commandes** reçues
- **Revenus** générés
- **Commandes récentes** avec leur statut

## 📦 Gestion des produits

### Ajouter un nouveau produit :

1. **Cliquez sur "Produits"** dans le menu de gauche
2. **Cliquez sur "Ajouter un produit"**
3. **Remplissez le formulaire** :

#### Informations de base :
- **Nom du produit** : Ex: "Rolex Submariner Date"
- **SKU** : Code unique (utilisez "Générer" pour créer automatiquement)
- **Description** : Description détaillée du produit

#### Prix et stock :
- **Prix de vente** : Prix affiché aux clients
- **Prix original** : Prix barré (optionnel, pour les promotions)
- **Stock** : Quantité disponible

#### Classification :
- **Catégorie** : Luxe, Sport, Casual, Classique, Smartwatch
- **Marque** : Rolex, Omega, TAG Heuer, etc.
- **Genre** : Homme, Femme, Enfant, Unisexe

#### Caractéristiques :
- **Ajoutez des caractéristiques** : Ex: "Étanche 300m", "Mouvement automatique"
- **Poids** : Poids en grammes (optionnel)

#### Images :
- **Upload d'images** : Sélectionnez jusqu'à 5 images
- **Formats acceptés** : JPG, PNG, WebP
- **Taille maximum** : 5MB par image
- **Image principale** : La première image sera l'image principale
- **Aperçu en temps réel** : Visualisez vos images avant la création

#### Options :
- **Produit mis en avant** : Apparaîtra sur la page d'accueil
- **Produit actif** : Visible sur le site (décochez pour masquer)

### Génération automatique du SKU :
Le bouton "Générer" crée un SKU unique basé sur :
- **3 premières lettres de la marque** (ex: ROL pour Rolex)
- **3 premières lettres de la catégorie** (ex: LUX pour Luxe)
- **Numéro aléatoire** (ex: 001)
- **Résultat** : ROL-LUX-001

## 🎯 Conseils pour ajouter des produits

### Noms de produits :
- Utilisez des noms clairs et descriptifs
- Incluez la marque et le modèle
- Ex: "Rolex Submariner Date", "Omega Speedmaster Professional"

### Descriptions :
- Décrivez les caractéristiques principales
- Mentionnez les matériaux (acier, or, titane)
- Indiquez l'étanchéité et le type de mouvement
- Ajoutez des détails sur le design

### Prix :
- **Prix de vente** : Prix normal affiché
- **Prix original** : Utilisez seulement pour les promotions
- La différence créera automatiquement un badge "Promo"

### Stock :
- Mettez à jour régulièrement les quantités
- Un stock de 0 affichera "Indisponible"
- Le système vous alertera quand le stock est bas

### Caractéristiques importantes pour les montres :
- **Étanchéité** : Ex: "Étanche 100m", "Étanche 300m"
- **Mouvement** : Ex: "Mouvement automatique", "Quartz suisse"
- **Matériaux** : Ex: "Acier inoxydable", "Titane", "Or 18k"
- **Fonctions** : Ex: "Chronographe", "GMT", "Alarme"
- **Verre** : Ex: "Verre saphir", "Verre minéral"

### Conseils pour les images :

#### Qualité des photos :
- **Résolution** : Minimum 800x800 pixels
- **Éclairage** : Photos bien éclairées, éviter les ombres
- **Arrière-plan** : Fond neutre (blanc ou gris clair)
- **Netteté** : Images nettes et bien cadrées

#### Types de photos recommandées :
1. **Vue de face** : Cadran bien visible (image principale)
2. **Vue de profil** : Épaisseur et bracelet
3. **Vue arrière** : Fond de boîtier si intéressant
4. **Détails** : Couronne, poussoirs, gravures
5. **Sur poignet** : Pour montrer l'échelle

#### Optimisation :
- **Compression** : Utilisez des outils pour réduire la taille
- **Format** : JPG pour les photos, PNG pour les logos
- **Nommage** : Noms descriptifs pour le SEO

## 🏷️ Catégories disponibles

### Luxe
- Montres haut de gamme (Rolex, Patek Philippe, etc.)
- Prix généralement > 5000€

### Sport
- Montres de plongée, chronographes
- Résistantes et fonctionnelles

### Casual
- Montres décontractées pour tous les jours
- Prix abordables, design simple

### Classique
- Montres élégantes et intemporelles
- Pour occasions formelles

### Smartwatch
- Montres connectées (Apple Watch, etc.)
- Fonctions technologiques avancées

## 🔧 Fonctionnalités à venir

### Prochaines mises à jour :
- **Gestion des images** : Upload d'images produits
- **Gestion des commandes** : Suivi et traitement
- **Gestion des utilisateurs** : Administration des comptes
- **Statistiques avancées** : Rapports de vente
- **Gestion des codes promo** : Création et modification
- **Paramètres boutique** : Configuration générale

## 🚨 Important

### Sauvegarde :
- Les données sont automatiquement sauvegardées dans MySQL
- Pensez à faire des sauvegardes régulières de votre base de données

### Sécurité :
- L'accès admin est actuellement ouvert à tous les utilisateurs connectés
- En production, ajoutez un système de rôles (admin/client)

### Performance :
- Optimisez les images avant de les ajouter
- Utilisez des descriptions concises mais informatives
- Maintenez un stock à jour pour éviter les commandes impossibles

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez que XAMPP et MySQL sont démarrés
2. Vérifiez que vous êtes connecté avec un compte valide
3. Consultez la console du navigateur pour les erreurs
4. Redémarrez le serveur de développement si nécessaire

---

**Bonne gestion de votre boutique Oussama Watches ! 🕰️**
