export interface Product {
  id: string
  name: string
  description: string
  price: number
  images: string[]
  category: string
  brand: string
  gender: 'homme' | 'femme' | 'enfant' | 'unisexe'
  stock: number
  features: string[]
  created_at: string
  updated_at: string
}

export interface Category {
  id: string
  name: string
  description?: string
  image?: string
}

export interface CartItem {
  product: Product
  quantity: number
}

export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  address?: Address
  created_at: string
}

export interface Address {
  street: string
  city: string
  postal_code: string
  country: string
}

export interface Order {
  id: string
  user_id: string
  items: OrderItem[]
  total: number
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
  shipping_address: Address
  payment_status: 'pending' | 'paid' | 'failed'
  payment_method: 'stripe' | 'paypal'
  created_at: string
  updated_at: string
}

export interface OrderItem {
  product_id: string
  product_name: string
  product_image: string
  quantity: number
  price: number
}

export interface FilterOptions {
  category?: string
  brand?: string
  gender?: string
  minPrice?: number
  maxPrice?: number
  search?: string
}
