import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET - Récupérer le panier d'un utilisateur
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'ID utilisateur requis' },
        { status: 400 }
      )
    }

    const cartItems = await prisma.cartItem.findMany({
      where: { userId },
      include: {
        product: {
          include: {
            brand: true,
            images: true
          }
        }
      }
    })

    return NextResponse.json({ cartItems })

  } catch (error) {
    console.error('Erreur lors de la récupération du panier:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération du panier' },
      { status: 500 }
    )
  }
}

// POST - Ajouter un produit au panier
export async function POST(request: NextRequest) {
  try {
    const { userId, productId, quantity = 1 } = await request.json()

    if (!userId || !productId) {
      return NextResponse.json(
        { error: 'ID utilisateur et ID produit requis' },
        { status: 400 }
      )
    }

    // Vérifier si le produit existe
    const product = await prisma.product.findUnique({
      where: { id: productId }
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Produit non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier si l'article est déjà dans le panier
    const existingCartItem = await prisma.cartItem.findUnique({
      where: {
        userId_productId: {
          userId,
          productId
        }
      }
    })

    let cartItem

    if (existingCartItem) {
      // Mettre à jour la quantité
      cartItem = await prisma.cartItem.update({
        where: { id: existingCartItem.id },
        data: { quantity: existingCartItem.quantity + quantity },
        include: {
          product: {
            include: {
              brand: true,
              images: true
            }
          }
        }
      })
    } else {
      // Créer un nouvel article dans le panier
      cartItem = await prisma.cartItem.create({
        data: {
          userId,
          productId,
          quantity
        },
        include: {
          product: {
            include: {
              brand: true,
              images: true
            }
          }
        }
      })
    }

    return NextResponse.json({
      message: 'Produit ajouté au panier',
      cartItem
    })

  } catch (error) {
    console.error('Erreur lors de l\'ajout au panier:', error)
    return NextResponse.json(
      { error: 'Erreur lors de l\'ajout au panier' },
      { status: 500 }
    )
  }
}

// PUT - Mettre à jour la quantité d'un article
export async function PUT(request: NextRequest) {
  try {
    const { cartItemId, quantity } = await request.json()

    if (!cartItemId || quantity < 0) {
      return NextResponse.json(
        { error: 'ID article et quantité valide requis' },
        { status: 400 }
      )
    }

    if (quantity === 0) {
      // Supprimer l'article si quantité = 0
      await prisma.cartItem.delete({
        where: { id: cartItemId }
      })
      return NextResponse.json({ message: 'Article supprimé du panier' })
    }

    const cartItem = await prisma.cartItem.update({
      where: { id: cartItemId },
      data: { quantity },
      include: {
        product: {
          include: {
            brand: true,
            images: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Quantité mise à jour',
      cartItem
    })

  } catch (error) {
    console.error('Erreur lors de la mise à jour:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la mise à jour' },
      { status: 500 }
    )
  }
}

// DELETE - Supprimer un article du panier
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const cartItemId = searchParams.get('cartItemId')

    if (!cartItemId) {
      return NextResponse.json(
        { error: 'ID article requis' },
        { status: 400 }
      )
    }

    await prisma.cartItem.delete({
      where: { id: cartItemId }
    })

    return NextResponse.json({ message: 'Article supprimé du panier' })

  } catch (error) {
    console.error('Erreur lors de la suppression:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la suppression' },
      { status: 500 }
    )
  }
}
