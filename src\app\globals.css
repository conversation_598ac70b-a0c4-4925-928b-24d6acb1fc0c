@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Correction de la couleur du texte des inputs - FORCE NOIR */
input,
textarea,
select {
  color: #000000 !important; /* Noir pur */
  -webkit-text-fill-color: #000000 !important; /* Pour Safari */
}

/* Spécifique pour tous les types d'inputs */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="file"],
input[type="date"],
input[type="time"],
input[type="datetime-local"] {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
}

/* Placeholders en gris */
input::placeholder,
textarea::placeholder {
  color: #6b7280 !important; /* text-gray-500 */
  opacity: 1 !important;
}

/* Focus - maintenir le noir */
input:focus,
textarea:focus,
select:focus {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
}

/* Inputs désactivés */
input:disabled,
textarea:disabled,
select:disabled {
  color: #4b5563 !important;
  -webkit-text-fill-color: #4b5563 !important;
  background-color: #f3f4f6 !important;
}

/* Correction spécifique pour les classes Tailwind */
.text-gray-400,
.text-gray-500,
.text-gray-600 {
  color: #000000 !important;
}

/* Forcer le noir sur tous les inputs avec classes CSS */
input.px-3,
input.py-2,
textarea.px-3,
textarea.py-2,
select.px-3,
select.py-2 {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
}

/* Classe utilitaire pour forcer le texte noir */
.input-black-text {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
}

.input-black-text:focus {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
}

.input-black-text::placeholder {
  color: #6b7280 !important;
  opacity: 1 !important;
}

/* Animations personnalisées pour la navbar */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Délais d'animation */
.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

.animation-delay-800 {
  animation-delay: 800ms;
}

.animation-delay-1000 {
  animation-delay: 1000ms;
}

.animation-delay-1200 {
  animation-delay: 1200ms;
}

/* Effet de survol pour les liens de navigation */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.nav-link:hover::before {
  width: 100%;
}

/* Effet de particules pour le logo */
.logo-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #3b82f6, #8b5cf6);
  border-radius: 50%;
  animation: float 2s ease-in-out infinite;
}

/* Effet glassmorphism */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-light {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.glass-medium {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Styles pour les filtres luxueux */
.filter-select {
  background-image: none;
}

.filter-select::-ms-expand {
  display: none;
}

.filter-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.filter-label-icon {
  animation: pulse 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

.shimmer-effect {
  animation: shimmer 2s infinite;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
