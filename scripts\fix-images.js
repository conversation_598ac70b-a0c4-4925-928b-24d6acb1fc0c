const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixImages() {
  try {
    console.log('🔧 Correction des images...')

    // Mettre à jour toutes les images avec des URLs Unsplash qui fonctionnent
    const images = await prisma.productImage.findMany()
    
    const newImageUrls = [
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1533139502658-0198f920d8e8?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1508057198894-247b23fe5ade?w=400&h=400&fit=crop'
    ]

    for (let i = 0; i < images.length; i++) {
      const image = images[i]
      const newUrl = newImageUrls[i % newImageUrls.length]
      
      await prisma.productImage.update({
        where: { id: image.id },
        data: { url: newUrl }
      })
      
      console.log(`✅ Image ${i + 1} mise à jour: ${newUrl}`)
    }

    console.log('🎉 Toutes les images ont été corrigées !')

  } catch (error) {
    console.error('❌ Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixImages()
