{"name": "montres", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma db push --force-reset && npm run db:seed"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.12.0", "@stripe/stripe-js": "^7.4.0", "@supabase/supabase-js": "^2.51.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "jose": "^6.0.11", "lucide-react": "^0.525.0", "mysql2": "^3.14.2", "next": "15.4.1", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}