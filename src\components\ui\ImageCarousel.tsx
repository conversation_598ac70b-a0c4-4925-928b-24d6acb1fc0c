'use client'

import { useState, useEffect } from 'react'

interface ImageCarouselProps {
  images: string[]
  fallbackImages: string[]
  interval?: number
}

export default function ImageCarousel({ 
  images, 
  fallbackImages, 
  interval = 3000 
}: ImageCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === images.length - 1 ? 0 : prevIndex + 1
      )
    }, interval)

    return () => clearInterval(timer)
  }, [images.length, interval])

  return (
    <div className="absolute inset-0">
      {images.map((image, index) => (
        <div
          key={index}
          className={`absolute inset-0 bg-cover bg-center transition-opacity duration-1000 ${
            index === currentIndex ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            backgroundImage: `url(${image}), url(${fallbackImages[index]})`,
            filter: 'blur(1px)',
          }}
        />
      ))}

      {/* Effet glassmorphism pour améliorer la lisibilité */}
      <div className="absolute inset-0 backdrop-blur-sm"></div>

      {/* Indicateurs de carrousel */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 backdrop-blur-sm bg-white bg-opacity-10 rounded-full px-4 py-2">
        {images.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentIndex
                ? 'bg-white scale-125 shadow-lg'
                : 'bg-white bg-opacity-40 hover:bg-opacity-70'
            }`}
          />
        ))}
      </div>
    </div>
  )
}
