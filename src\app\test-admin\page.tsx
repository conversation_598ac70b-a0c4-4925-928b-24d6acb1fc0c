'use client'

import { useState } from 'react'

export default function TestAdminPage() {
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)

  const testDatabase = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/categories')
      const data = await response.json()
      setResult(`Catégories: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      setResult(`Erreur: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testBrands = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/brands')
      const data = await response.json()
      setResult(`Marques: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      setResult(`Erreur: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testProducts = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/products')
      const data = await response.json()
      setResult(`Produits: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      setResult(`Erreur: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Test Admin API</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Tests API</h2>
          
          <div className="space-x-4 mb-6">
            <button
              onClick={testDatabase}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              Test Catégories
            </button>
            
            <button
              onClick={testBrands}
              disabled={loading}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
            >
              Test Marques
            </button>
            
            <button
              onClick={testProducts}
              disabled={loading}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50"
            >
              Test Produits
            </button>
          </div>
          
          {loading && (
            <div className="text-blue-600 mb-4">Chargement...</div>
          )}
          
          {result && (
            <div className="bg-gray-100 p-4 rounded">
              <h3 className="font-semibold mb-2">Résultat :</h3>
              <pre className="text-sm overflow-auto">{result}</pre>
            </div>
          )}
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Informations système</h2>
          <div className="space-y-2 text-sm">
            <p><strong>URL actuelle :</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
            <p><strong>User Agent :</strong> {typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'}</p>
            <p><strong>Timestamp :</strong> {new Date().toISOString()}</p>
          </div>
        </div>
      </div>
    </div>
  )
}
