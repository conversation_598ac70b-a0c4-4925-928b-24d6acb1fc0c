'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import DatabaseStatus from '@/components/DatabaseStatus'
import { Search, Filter, Grid, List, Star, ShoppingCart, Heart, RefreshCw } from 'lucide-react'
import { formatPrice } from '@/lib/utils'
import { getProducts, getCategories, getBrands } from '@/lib/database'
import { useCart } from '@/hooks/useCart'
import { useWishlist } from '@/hooks/useWishlist'



export default function MontresPage() {
  const searchParams = useSearchParams()
  const { addToCart } = useCart()
  const { toggleWishlist, isInWishlist } = useWishlist()

  const [products, setProducts] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('name')
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    brand: '',
    minPrice: '',
    maxPrice: '',
    gender: searchParams?.get('gender') || ''
  })

  useEffect(() => {
    loadInitialData()
  }, [])

  useEffect(() => {
    loadProducts()
  }, [filters, sortBy])

  const loadInitialData = async () => {
    try {
      const [categoriesData, brandsData] = await Promise.all([
        getCategories(),
        getBrands()
      ])
      setCategories(categoriesData)
      setBrands(brandsData)
    } catch (error) {
      console.error('Error loading initial data:', error)
    }
  }

  const loadProducts = async () => {
    setLoading(true)
    try {
      // Construire l'URL avec les paramètres de filtre
      const params = new URLSearchParams()
      if (filters.search) params.append('search', filters.search)
      if (filters.category) params.append('category', filters.category)
      if (filters.brand) params.append('brand', filters.brand)
      if (filters.gender) params.append('gender', filters.gender)
      if (filters.minPrice) params.append('minPrice', filters.minPrice)
      if (filters.maxPrice) params.append('maxPrice', filters.maxPrice)

      // Appeler l'API pour récupérer les produits
      const response = await fetch(`/api/products?${params.toString()}`)
      const productsData = await response.json()

      // Apply sorting
      const sortedProducts = [...productsData].sort((a, b) => {
        switch (sortBy) {
          case 'price-asc':
            return a.price - b.price
          case 'price-desc':
            return b.price - a.price
          case 'rating':
            const avgRatingA = a.reviews?.length ? a.reviews.reduce((sum: number, r: any) => sum + r.rating, 0) / a.reviews.length : 0
            const avgRatingB = b.reviews?.length ? b.reviews.reduce((sum: number, r: any) => sum + r.rating, 0) / b.reviews.length : 0
            return avgRatingB - avgRatingA
          case 'name':
          default:
            return a.name.localeCompare(b.name)
        }
      })

      setProducts(sortedProducts)
    } catch (error) {
      console.error('Error loading products:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      brand: '',
      minPrice: '',
      maxPrice: '',
      gender: ''
    })
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Database Status */}
        <DatabaseStatus />

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Catalogue de Montres
            {filters.gender && (
              <span className="text-blue-600 ml-2">
                - {filters.gender === 'homme' ? 'Hommes' : filters.gender === 'femme' ? 'Femmes' : 'Enfants'}
              </span>
            )}
          </h1>
          <p className="text-gray-600">
            {loading ? 'Chargement...' : `${products.length} produit${products.length > 1 ? 's' : ''} trouvé${products.length > 1 ? 's' : ''}`}
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-1/4">
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Filtres</h2>
                <button
                  onClick={clearFilters}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Effacer tout
                </button>
              </div>

              {/* Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Recherche
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    placeholder="Nom ou marque..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                </div>
              </div>

              {/* Category */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Catégorie
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Toutes les catégories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Brand */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Marque
                </label>
                <select
                  value={filters.brand}
                  onChange={(e) => handleFilterChange('brand', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Toutes les marques</option>
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.name}>
                      {brand.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Price Range */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Prix (DH)
                </label>
                <div className="flex gap-2">
                  <input
                    type="number"
                    value={filters.minPrice}
                    onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                    placeholder="Min"
                    className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="number"
                    value={filters.maxPrice}
                    onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                    placeholder="Max"
                    className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            {/* Toolbar */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
              <div className="flex items-center gap-4">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="name">Trier par nom</option>
                  <option value="price-asc">Prix croissant</option>
                  <option value="price-desc">Prix décroissant</option>
                  <option value="rating">Mieux notés</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}`}
                >
                  <List className="h-5 w-5" />
                </button>
                <button
                  onClick={loadProducts}
                  disabled={loading}
                  className="p-2 rounded-lg bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 transition-colors"
                  title="Rafraîchir les produits"
                >
                  <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>

            {/* Products Grid/List */}
            {loading ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">Chargement des produits...</p>
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">Aucun produit trouvé avec ces critères.</p>
              </div>
            ) : (
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                {products.map((product) => {
                  const primaryImage = product.images?.find((img: any) => img.is_primary) || product.images?.[0]
                  const avgRating = product.reviews?.length
                    ? product.reviews.reduce((sum: number, r: any) => sum + r.rating, 0) / product.reviews.length
                    : 0
                  const reviewCount = product.reviews?.length || 0

                  // Debug: Log des images pour ce produit
                  if (product.name === 'OUSSAMA') {
                    console.log('Produit OUSSAMA - Images:', product.images)
                    console.log('Produit OUSSAMA - Image principale:', primaryImage)
                  }

                  return (
                  <div
                    key={product.id}
                    className={`bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow ${
                      viewMode === 'list' ? 'flex' : ''
                    }`}
                  >
                    <div className={viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}>
                      <div className="relative">
                        <div className="bg-gray-200 h-64 flex items-center justify-center">
                          {primaryImage ? (
                            <img
                              src={primaryImage.image_url}
                              alt={primaryImage.alt_text || product.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                console.error('Erreur chargement image:', primaryImage.image_url)
                                e.currentTarget.style.display = 'none'
                                e.currentTarget.parentElement!.innerHTML = `<span class="text-gray-500 text-center px-4">Erreur image: ${product.name}</span>`
                              }}
                              onLoad={() => {
                                console.log('Image chargée avec succès:', primaryImage.image_url)
                              }}
                            />
                          ) : (
                            <span className="text-gray-500 text-center px-4">Aucune image pour {product.name}</span>
                          )}
                        </div>

                        {/* Wishlist button */}
                        <button
                          onClick={() => toggleWishlist(product.id)}
                          className={`absolute top-2 right-2 p-2 rounded-full transition-colors ${
                            isInWishlist(product.id)
                              ? 'bg-red-500 text-white'
                              : 'bg-white text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <Heart className={`h-4 w-4 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                        </button>

                        {product.is_featured && (
                          <span className="absolute top-2 left-2 bg-green-600 text-white px-2 py-1 text-xs rounded">
                            Nouveau
                          </span>
                        )}
                        {product.original_price && product.original_price > product.price && (
                          <span className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 text-xs rounded">
                            Promo
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="p-4 flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900">{product.name}</h3>
                          <p className="text-sm text-gray-600">{product.brand?.name}</p>
                        </div>
                        {product.stock_quantity === 0 && (
                          <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">
                            Rupture
                          </span>
                        )}
                      </div>

                      <div className="flex items-center mb-2">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-sm text-gray-600 ml-1">
                            {avgRating > 0 ? avgRating.toFixed(1) : 'N/A'} ({reviewCount} avis)
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <span className="text-lg font-bold text-gray-900">
                            {formatPrice(product.price)}
                          </span>
                          {product.original_price && product.original_price > product.price && (
                            <span className="text-sm text-gray-500 line-through ml-2">
                              {formatPrice(product.original_price)}
                            </span>
                          )}
                        </div>

                        <button
                          disabled={product.stock_quantity === 0}
                          onClick={() => addToCart(product.id)}
                          className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            product.stock_quantity > 0
                              ? 'bg-blue-600 text-white hover:bg-blue-700'
                              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          }`}
                        >
                          <ShoppingCart className="h-4 w-4 mr-1" />
                          {product.stock_quantity > 0 ? 'Ajouter' : 'Indisponible'}
                        </button>
                      </div>
                    </div>
                  </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
