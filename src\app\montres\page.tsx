'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import DatabaseStatus from '@/components/DatabaseStatus'
import { Search, Filter, Grid, List, Star, ShoppingCart, Heart, RefreshCw } from 'lucide-react'
import { formatPrice } from '@/lib/utils'
import { getProducts, getCategories, getBrands } from '@/lib/database'
import { useCart } from '@/hooks/useCart'
import { useWishlist } from '@/hooks/useWishlist'
import { useAuth } from '@/hooks/useAuth'



export default function MontresPage() {
  const searchParams = useSearchParams()
  const { user } = useAuth()
  const { addToCart } = useCart()
  const { toggleWishlist, isInWishlist } = useWishlist()

  const [products, setProducts] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [brands, setBrands] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('name')
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    brand: '',
    minPrice: '',
    maxPrice: '',
    gender: searchParams?.get('gender') || ''
  })

  useEffect(() => {
    loadInitialData()
  }, [])

  useEffect(() => {
    loadProducts()
  }, [filters, sortBy])

  const loadInitialData = async () => {
    try {
      const [categoriesData, brandsData] = await Promise.all([
        getCategories(),
        getBrands()
      ])
      setCategories(categoriesData)
      setBrands(brandsData)
    } catch (error) {
      console.error('Error loading initial data:', error)
    }
  }

  const loadProducts = async () => {
    setLoading(true)
    try {
      // Construire l'URL avec les paramètres de filtre
      const params = new URLSearchParams()
      if (filters.search) params.append('search', filters.search)
      if (filters.category) params.append('category', filters.category)
      if (filters.brand) params.append('brand', filters.brand)
      if (filters.gender) params.append('gender', filters.gender)
      if (filters.minPrice) params.append('minPrice', filters.minPrice)
      if (filters.maxPrice) params.append('maxPrice', filters.maxPrice)

      // Appeler l'API pour récupérer les produits
      const response = await fetch(`/api/products?${params.toString()}`)
      const productsData = await response.json()

      // Apply sorting
      const sortedProducts = [...productsData].sort((a, b) => {
        switch (sortBy) {
          case 'price-asc':
            return a.price - b.price
          case 'price-desc':
            return b.price - a.price
          case 'rating':
            const avgRatingA = a.reviews?.length ? a.reviews.reduce((sum: number, r: any) => sum + r.rating, 0) / a.reviews.length : 0
            const avgRatingB = b.reviews?.length ? b.reviews.reduce((sum: number, r: any) => sum + r.rating, 0) / b.reviews.length : 0
            return avgRatingB - avgRatingA
          case 'name':
          default:
            return a.name.localeCompare(b.name)
        }
      })

      setProducts(sortedProducts)
    } catch (error) {
      console.error('Error loading products:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      brand: '',
      minPrice: '',
      maxPrice: '',
      gender: ''
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Database Status */}
        <DatabaseStatus />

        {/* Page Header */}
        <div className="mb-12 text-center">
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-4">
            Catalogue de Montres
            {filters.gender && (
              <span className="block text-3xl md:text-4xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mt-2">
                {filters.gender === 'homme' ? 'Collection Hommes' : filters.gender === 'femme' ? 'Collection Femmes' : 'Collection Enfants'}
              </span>
            )}
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6 rounded-full"></div>
          <p className="text-xl text-gray-600 font-light">
            {loading ? (
              <span className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
                Chargement de notre collection...
              </span>
            ) : (
              `${products.length} montre${products.length > 1 ? 's' : ''} d'exception ${products.length > 1 ? 'disponibles' : 'disponible'}`
            )}
          </p>
        </div>

        {/* Barre de recherche et filtres en haut */}
        <div className="mb-10">
          {/* Barre de recherche */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Rechercher une montre par nom ou marque..."
                className="w-full pl-14 pr-6 py-4 text-lg border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
              />
              <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
              {filters.search && (
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  ✕
                </button>
              )}
            </div>
            {filters.search && (
              <p className="text-center text-gray-600 mt-3">
                Recherche pour: <span className="font-semibold text-blue-600">"{filters.search}"</span>
              </p>
            )}
          </div>

          {/* Filtres horizontaux */}
          <div className="bg-gradient-to-r from-white to-gray-50 p-6 rounded-3xl shadow-xl border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">Filtres</h2>
              <button
                onClick={clearFilters}
                className="text-sm font-semibold text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 px-4 py-2 rounded-full transition-all duration-300"
              >
                Effacer tout
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Catégorie */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Catégorie
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-300"
                >
                  <option value="">Toutes les catégories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Marque */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Marque
                </label>
                <select
                  value={filters.brand}
                  onChange={(e) => handleFilterChange('brand', e.target.value)}
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-300"
                >
                  <option value="">Toutes les marques</option>
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.name}>
                      {brand.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Prix Min */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Prix minimum (DH)
                </label>
                <input
                  type="number"
                  value={filters.minPrice}
                  onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                  placeholder="Prix min..."
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-300"
                />
              </div>

              {/* Prix Max */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Prix maximum (DH)
                </label>
                <input
                  type="number"
                  value={filters.maxPrice}
                  onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                  placeholder="Prix max..."
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-300"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Contenu principal */}
        <div>
            {/* Toolbar */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
              <div className="flex items-center gap-4">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="name">Trier par nom</option>
                  <option value="price-asc">Prix croissant</option>
                  <option value="price-desc">Prix décroissant</option>
                  <option value="rating">Mieux notés</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}`}
                >
                  <List className="h-5 w-5" />
                </button>
                <button
                  onClick={loadProducts}
                  disabled={loading}
                  className="p-2 rounded-lg bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 transition-colors"
                  title="Rafraîchir les produits"
                >
                  <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>

            {/* Products Grid/List */}
            {loading ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">Chargement des produits...</p>
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">Aucun produit trouvé avec ces critères.</p>
              </div>
            ) : (
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-8' : 'space-y-6'}>
                {products.map((product) => {
                  const primaryImage = product.images?.find((img: any) => img.is_primary) || product.images?.[0]
                  const avgRating = product.reviews?.length
                    ? product.reviews.reduce((sum: number, r: any) => sum + r.rating, 0) / product.reviews.length
                    : 0
                  const reviewCount = product.reviews?.length || 0

                  // Debug: Log des images pour ce produit
                  if (product.name === 'OUSSAMA') {
                    console.log('Produit OUSSAMA - Images:', product.images)
                    console.log('Produit OUSSAMA - Image principale:', primaryImage)
                  }

                  return (
                  <div
                    key={product.id}
                    className={`bg-gradient-to-br from-white to-gray-50 border border-gray-100 rounded-3xl overflow-hidden hover:shadow-2xl hover:border-gray-200 transition-all duration-500 transform hover:-translate-y-2 group ${
                      viewMode === 'list' ? 'flex' : ''
                    }`}
                  >
                    <div className={viewMode === 'list' ? 'w-64 flex-shrink-0' : ''}>
                      <div className="relative overflow-hidden">
                        <div className="bg-gradient-to-br from-gray-100 to-gray-200 h-80 flex items-center justify-center relative">
                          {primaryImage ? (
                            <img
                              src={primaryImage.image_url}
                              alt={primaryImage.alt_text || product.name}
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                              onError={(e) => {
                                console.error('Erreur chargement image:', primaryImage.image_url)
                                e.currentTarget.style.display = 'none'
                                e.currentTarget.parentElement!.innerHTML = `<div class="flex flex-col items-center justify-center h-full text-gray-500"><div class="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mb-4"><span class="text-2xl">⌚</span></div><span class="text-center px-4 font-medium">Image non disponible</span><span class="text-sm text-gray-400">${product.name}</span></div>`
                              }}
                              onLoad={() => {
                                console.log('Image chargée avec succès:', primaryImage.image_url)
                              }}
                            />
                          ) : (
                            <div className="flex flex-col items-center justify-center h-full text-gray-500">
                              <div className="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mb-4">
                                <span className="text-3xl">⌚</span>
                              </div>
                              <span className="text-center px-4 font-medium">Aucune image</span>
                              <span className="text-sm text-gray-400">{product.name}</span>
                            </div>
                          )}

                          {/* Overlay gradient */}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>

                        {/* Wishlist button */}
                        <button
                          onClick={() => toggleWishlist(product.id)}
                          className={`absolute top-4 right-4 p-3 rounded-full backdrop-blur-sm transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-110 ${
                            isInWishlist(product.id)
                              ? 'bg-red-500 text-white'
                              : 'bg-white/90 text-gray-600 hover:bg-white'
                          }`}
                        >
                          <Heart className={`h-5 w-5 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                        </button>

                        {product.is_featured && (
                          <span className="absolute top-4 left-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 text-sm font-bold rounded-full shadow-lg backdrop-blur-sm">
                            ✨ Nouveau
                          </span>
                        )}
                        {product.original_price && product.original_price > product.price && (
                          <span className="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 text-sm font-bold rounded-full shadow-lg backdrop-blur-sm animate-pulse">
                            🔥 Promo
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="p-6 flex-1">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">{product.name}</h3>
                          <p className="text-lg text-gray-500 font-medium">{product.brand?.name}</p>
                        </div>
                        {product.stock_quantity === 0 && (
                          <span className="text-sm bg-red-100 text-red-600 px-3 py-1 rounded-full font-medium">
                            Rupture
                          </span>
                        )}
                      </div>

                      <div className="flex items-center mb-4">
                        <div className="flex items-center bg-yellow-50 px-3 py-1 rounded-full">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="text-sm text-gray-700 ml-1 font-medium">
                            {avgRating > 0 ? avgRating.toFixed(1) : 'N/A'} ({reviewCount} avis)
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex flex-col">
                          <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            {formatPrice(product.price)}
                          </span>
                          {product.original_price && product.original_price > product.price && (
                            <span className="text-lg text-gray-400 line-through">
                              {formatPrice(product.original_price)}
                            </span>
                          )}
                        </div>

                        <button
                          disabled={product.stock_quantity === 0}
                          onClick={async () => {
                            if (!user) {
                              alert('Vous devez être connecté pour ajouter des produits au panier')
                              return
                            }
                            const success = await addToCart(product.id)
                            if (success) {
                              alert('✅ Produit ajouté au panier !')
                            }
                          }}
                          className={`flex items-center px-6 py-3 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
                            product.stock_quantity > 0
                              ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                              : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                          }`}
                        >
                          <ShoppingCart className="h-5 w-5 mr-2" />
                          {product.stock_quantity > 0 ? 'Ajouter' : 'Indisponible'}
                        </button>
                      </div>
                    </div>
                  </div>
                  )
                })}
              </div>
            )}
        </div>
      </div>

      <Footer />
    </div>
  )
}
