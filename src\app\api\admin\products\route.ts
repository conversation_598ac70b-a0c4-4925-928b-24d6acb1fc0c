import { NextRequest, NextResponse } from 'next/server'
import { prisma, isDatabaseConfigured } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    if (!isDatabaseConfigured) {
      return NextResponse.json(
        { error: 'Base de données non configurée' },
        { status: 500 }
      )
    }

    const productData = await request.json()

    // Validation des données requises
    if (!productData.name || !productData.price || !productData.sku) {
      return NextResponse.json(
        { error: 'Nom, prix et SKU sont requis' },
        { status: 400 }
      )
    }

    // Vérifier si le SKU existe déjà
    const existingProduct = await prisma.product.findUnique({
      where: { sku: productData.sku }
    })

    if (existingProduct) {
      return NextResponse.json(
        { error: 'Ce SKU existe déjà' },
        { status: 400 }
      )
    }

    // Créer le produit
    const product = await prisma.product.create({
      data: {
        name: productData.name,
        description: productData.description || null,
        price: productData.price,
        originalPrice: productData.originalPrice || null,
        categoryId: productData.categoryId,
        brandId: productData.brandId,
        gender: productData.gender,
        stockQuantity: productData.stockQuantity || 0,
        sku: productData.sku,
        weight: productData.weight || null,
        features: productData.features || [],
        isFeatured: productData.isFeatured || false,
        isActive: productData.isActive !== false, // true par défaut
        metaTitle: productData.metaTitle || null,
        metaDescription: productData.metaDescription || null
      },
      include: {
        category: { select: { name: true } },
        brand: { select: { name: true } }
      }
    })

    // Créer une image par défaut seulement si aucune image n'est uploadée
    // (Les images seront ajoutées via l'API upload-image)

    return NextResponse.json({
      message: 'Produit créé avec succès',
      product
    })

  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la création du produit' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    if (!isDatabaseConfigured) {
      return NextResponse.json([])
    }

    const products = await prisma.product.findMany({
      include: {
        category: { select: { name: true } },
        brand: { select: { name: true } },
        images: {
          select: {
            id: true,
            imageUrl: true,
            altText: true,
            isPrimary: true,
            sortOrder: true
          },
          orderBy: { sortOrder: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(products)

  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des produits' },
      { status: 500 }
    )
  }
}
