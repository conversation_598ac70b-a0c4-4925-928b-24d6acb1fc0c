# Test : Admin → Catalogue

## 🧪 Test de synchronisation Admin/Catalogue

### Objectif :
Vérifier que les produits créés dans l'admin apparaissent immédiatement dans le catalogue.

### Étapes de test :

#### 1. Préparation
- [ ] XAMPP démarré (Apache + MySQL)
- [ ] Base de données `oussama_watches` créée
- [ ] Tables créées avec `npx prisma db push`
- [ ] Données de base insérées avec `npx prisma db seed`
- [ ] Serveur Next.js démarré avec `npm run dev`

#### 2. Connexion admin
- [ ] Aller sur http://localhost:3000
- [ ] Se connecter avec : <EMAIL> / password123
- [ ] Cliquer sur l'icône engrenage (⚙️) dans le header
- [ ] Vérifier l'accès à la page admin

#### 3. État initial du catalogue
- [ ] Aller sur http://localhost:3000/montres
- [ ] Noter le nombre de produits affichés
- [ ] Vérifier que les 6 produits de base sont présents :
  - Rolex Submariner Date
  - Omega Speedmaster Professional
  - TAG Heuer Formula 1
  - Casio G-Shock GA-2100
  - Citizen Eco-Drive Titanium
  - Apple Watch Series 9

#### 4. Création d'un nouveau produit
- [ ] Retourner à l'admin (/admin)
- [ ] Cliquer sur "Produits" puis "Ajouter un produit"
- [ ] Remplir le formulaire avec ces données de test :

**Informations de base :**
- Nom : "Seiko Prospex Solar"
- Description : "Montre solaire robuste pour les aventuriers"

**Prix et stock :**
- Prix de vente : 350
- Stock : 10

**Classification :**
- Catégorie : Sport
- Marque : Seiko
- Genre : Homme

**Caractéristiques :**
- "Énergie solaire"
- "Étanche 200m"
- "Boussole intégrée"

**Options :**
- [x] Produit actif
- [ ] Produit mis en avant

- [ ] Cliquer sur "Générer" pour le SKU
- [ ] Cliquer sur "Créer le produit"
- [ ] Vérifier le message de succès

#### 5. Vérification dans le catalogue
- [ ] Aller sur http://localhost:3000/montres
- [ ] Cliquer sur le bouton de rafraîchissement (🔄) vert
- [ ] Vérifier que le nouveau produit "Seiko Prospex Solar" apparaît
- [ ] Vérifier que le nombre total de produits a augmenté
- [ ] Tester les filtres :
  - [ ] Filtrer par marque "Seiko" → doit montrer le nouveau produit
  - [ ] Filtrer par catégorie "Sport" → doit inclure le nouveau produit

#### 6. Test des fonctionnalités
- [ ] Ajouter le nouveau produit au panier
- [ ] Ajouter le nouveau produit à la wishlist
- [ ] Vérifier que les compteurs se mettent à jour

#### 7. Test de création multiple
- [ ] Créer un deuxième produit avec ces données :

**Produit 2 :**
- Nom : "Tissot PRC 200"
- Prix : 280
- Catégorie : Sport
- Marque : Tissot (si disponible, sinon choisir une autre)
- Stock : 5

- [ ] Retourner au catalogue
- [ ] Rafraîchir
- [ ] Vérifier que les 2 nouveaux produits sont présents

### ✅ Résultats attendus :

1. **Création réussie** : Message de succès dans l'admin
2. **Synchronisation immédiate** : Produits visibles dans le catalogue après rafraîchissement
3. **Filtres fonctionnels** : Nouveaux produits apparaissent dans les filtres appropriés
4. **Compteurs mis à jour** : Nombre total de produits correct
5. **Fonctionnalités actives** : Panier et wishlist fonctionnent avec les nouveaux produits

### 🐛 Problèmes possibles :

#### Si les produits n'apparaissent pas :
1. Vérifier que la base de données est connectée
2. Vérifier les logs de la console (F12)
3. Cliquer sur le bouton de rafraîchissement vert
4. Redémarrer le serveur Next.js

#### Si l'admin ne fonctionne pas :
1. Vérifier la connexion utilisateur
2. Vérifier que XAMPP MySQL est démarré
3. Vérifier les variables d'environnement (.env.local)

#### Si les API ne répondent pas :
1. Vérifier les routes API dans /api/admin/
2. Vérifier les permissions de la base de données
3. Consulter les logs du serveur

### 📊 Métriques de succès :

- [ ] **Temps de création** : < 2 minutes par produit
- [ ] **Synchronisation** : Immédiate après rafraîchissement
- [ ] **Filtres** : Fonctionnent correctement
- [ ] **Performance** : Pas de ralentissement notable
- [ ] **Stabilité** : Pas d'erreurs dans la console

---

**Une fois ce test réussi, votre système admin → catalogue est pleinement fonctionnel ! 🎉**
