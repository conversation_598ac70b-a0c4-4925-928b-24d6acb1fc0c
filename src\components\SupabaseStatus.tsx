'use client'

import { isSupabaseConfigured } from '@/lib/supabase'
import { AlertTriangle, Database, ExternalLink } from 'lucide-react'

export default function SupabaseStatus() {
  if (isSupabaseConfigured) {
    return null
  }

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-yellow-400" />
        </div>
        <div className="ml-3">
          <p className="text-sm text-yellow-700">
            <strong>Mode démonstration :</strong> Supabase n'est pas configuré. 
            Les données affichées sont des exemples.
          </p>
          <div className="mt-2">
            <div className="text-sm">
              <a
                href="/SETUP_SUPABASE.md"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-yellow-700 hover:text-yellow-600 underline"
              >
                <Database className="h-4 w-4 mr-1" />
                Voir le guide de configuration
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
