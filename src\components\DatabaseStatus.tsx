'use client'

import { useState } from 'react'
import { isDatabaseConfigured } from '@/lib/prisma'
import { X, Info, Database, Settings, Play } from 'lucide-react'

export default function DatabaseStatus() {
  const [isVisible, setIsVisible] = useState(!isDatabaseConfigured)

  if (!isVisible || isDatabaseConfigured) {
    return null
  }

  return (
    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
      <div className="flex">
        <div className="flex-shrink-0">
          <Database className="h-5 w-5 text-orange-400" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-orange-800">
            Configuration de la base de données requise
          </h3>
          <div className="mt-2 text-sm text-orange-700">
            <p className="mb-3">
              Pour utiliser toutes les fonctionnalités, vous devez configurer XAMPP et MySQL.
            </p>
            
            <div className="bg-orange-100 rounded-lg p-3 mb-3">
              <h4 className="font-medium text-orange-800 mb-2 flex items-center">
                <Play className="h-4 w-4 mr-2" />
                Étapes rapides :
              </h4>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Démarrez XAMPP (Apache + MySQL)</li>
                <li>Créez la base de données "oussama_watches" dans phpMyAdmin</li>
                <li>Exécutez : <code className="bg-orange-200 px-1 rounded">npx prisma db push</code></li>
                <li>Exécutez : <code className="bg-orange-200 px-1 rounded">npx prisma db seed</code></li>
              </ol>
            </div>

            <div className="space-y-1 text-sm">
              <p>✅ Interface utilisateur (mode démonstration)</p>
              <p>⏳ Base de données MySQL</p>
              <p>⏳ Authentification utilisateur</p>
              <p>⏳ Panier persistant</p>
              <p>⏳ Gestion des commandes</p>
            </div>

            <div className="mt-3">
              <a
                href="/SETUP_XAMPP.md"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-orange-600 hover:text-orange-800 underline text-sm font-medium"
              >
                <Settings className="h-3 w-3 mr-1" />
                Guide complet de configuration XAMPP
              </a>
            </div>
          </div>
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={() => setIsVisible(false)}
            className="bg-orange-50 rounded-md p-1.5 text-orange-400 hover:text-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}
