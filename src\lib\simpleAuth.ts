'use client'

// Système d'authentification simple
interface User {
  id: string
  firstName: string | null
  lastName: string | null
  email: string
  phone: string | null
}

// État global simple
let currentUser: User | null = null

// Charger l'utilisateur depuis localStorage au démarrage
if (typeof window !== 'undefined') {
  const savedUser = localStorage.getItem('user')
  if (savedUser) {
    try {
      currentUser = JSON.parse(savedUser)
    } catch (error) {
      console.error('Erreur lors du chargement de l\'utilisateur:', error)
      localStorage.removeItem('user')
    }
  }
}

// Fonction pour obtenir l'utilisateur actuel
export function getCurrentUser(): User | null {
  return currentUser
}

// Fonction de connexion
export async function loginUser(email: string, password: string): Promise<boolean> {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password })
    })

    const result = await response.json()

    if (response.ok) {
      currentUser = result.user
      localStorage.setItem('user', JSON.stringify(result.user))
      // Déclencher un événement pour notifier les composants
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('userChanged', { detail: currentUser }))
      }
      return true
    } else {
      console.error('Erreur de connexion:', result.error)
      return false
    }
  } catch (error) {
    console.error('Erreur lors de la connexion:', error)
    return false
  }
}

// Fonction de déconnexion
export function logoutUser(): void {
  currentUser = null
  localStorage.removeItem('user')
  // Déclencher un événement pour notifier les composants
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('userChanged', { detail: null }))
  }
}

// Fonction pour vérifier si l'utilisateur est connecté
export function isLoggedIn(): boolean {
  return currentUser !== null
}
