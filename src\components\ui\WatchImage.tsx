'use client'

import { useState } from 'react'

interface WatchImageProps {
  src: string
  fallbackSrc: string
  alt: string
  className?: string
}

export default function WatchImage({ src, fallbackSrc, alt, className = '' }: WatchImageProps) {
  const [imgSrc, setImgSrc] = useState(src)
  const [isLoading, setIsLoading] = useState(true)

  const handleError = () => {
    console.log(`Erreur chargement image: ${imgSrc}`)
    if (imgSrc !== fallbackSrc) {
      console.log(`Basculement vers fallback: ${fallbackSrc}`)
      setImgSrc(fallbackSrc)
    } else {
      console.log('Erreur même avec fallback')
    }
  }

  const handleLoad = () => {
    setIsLoading(false)
  }

  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-lg flex items-center justify-center">
          <div className="text-gray-400">Chargement...</div>
        </div>
      )}
      <img
        src={imgSrc}
        alt={alt}
        className={className}
        onError={handleError}
        onLoad={handleLoad}
      />
    </div>
  )
}
