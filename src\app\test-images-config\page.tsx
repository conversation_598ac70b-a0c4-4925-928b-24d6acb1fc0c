'use client'

import { useState } from 'react'

export default function TestImagesConfigPage() {
  const [imageStatus, setImageStatus] = useState<{[key: string]: 'loading' | 'success' | 'error'}>({})

  const images = [
    {
      name: 'rolex-submariner.jpg',
      title: 'Rolex Submariner',
      description: 'Photo 1 de la galerie'
    },
    {
      name: 'rolex-daytona.jpg', 
      title: 'Rolex Daytona',
      description: 'Photo 2 de la galerie'
    },
    {
      name: 'rolex-datejust.jpg',
      title: 'Rolex Datejust', 
      description: 'Photo 3 de la galerie'
    },
    {
      name: 'hero-bg.jpg',
      title: 'Arrière-plan Hero',
      description: 'Image de fond de la section bienvenue'
    }
  ]

  const handleImageLoad = (imageName: string) => {
    setImageStatus(prev => ({ ...prev, [imageName]: 'success' }))
  }

  const handleImageError = (imageName: string) => {
    setImageStatus(prev => ({ ...prev, [imageName]: 'error' }))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100'
      case 'error': return 'text-red-600 bg-red-100'
      default: return 'text-yellow-600 bg-yellow-100'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success': return '✅ Trouvée'
      case 'error': return '❌ Manquante'
      default: return '⏳ Vérification...'
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow p-8 mb-8">
          <h1 className="text-3xl font-bold mb-6">🔧 Configuration des Images</h1>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-blue-800 mb-4">📋 Instructions</h2>
            <ol className="list-decimal list-inside space-y-2 text-blue-700">
              <li>Placez vos photos dans le dossier <code className="bg-blue-200 px-2 py-1 rounded">public/images/</code></li>
              <li>Utilisez les noms de fichiers EXACTS indiqués ci-dessous</li>
              <li>Rafraîchissez cette page pour vérifier</li>
              <li>Les images avec ✅ sont correctement configurées</li>
            </ol>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {images.map((image) => (
              <div key={image.name} className="border border-gray-200 rounded-lg p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">{image.title}</h3>
                    <p className="text-gray-600 text-sm">{image.description}</p>
                    <code className="text-sm bg-gray-100 px-2 py-1 rounded mt-2 inline-block">
                      {image.name}
                    </code>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(imageStatus[image.name] || 'loading')}`}>
                    {getStatusText(imageStatus[image.name] || 'loading')}
                  </span>
                </div>

                <div className="bg-gray-100 rounded-lg h-48 flex items-center justify-center overflow-hidden">
                  <img
                    src={`/images/${image.name}`}
                    alt={image.title}
                    className="max-w-full max-h-full object-cover"
                    onLoad={() => handleImageLoad(image.name)}
                    onError={() => handleImageError(image.name)}
                  />
                </div>

                {imageStatus[image.name] === 'error' && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                    <strong>Image manquante !</strong><br/>
                    Placez votre photo nommée <code>{image.name}</code> dans le dossier <code>public/images/</code>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-8 bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">📊 Résumé</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {Object.values(imageStatus).filter(s => s === 'success').length}
                </div>
                <div className="text-sm text-gray-600">Images trouvées</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {Object.values(imageStatus).filter(s => s === 'error').length}
                </div>
                <div className="text-sm text-gray-600">Images manquantes</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {images.length}
                </div>
                <div className="text-sm text-gray-600">Total requis</div>
              </div>
            </div>
          </div>

          <div className="mt-8 flex space-x-4">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              🔄 Revérifier les images
            </button>
            <a
              href="/"
              className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              🏠 Voir le site
            </a>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-8">
          <h2 className="text-xl font-semibold mb-4">💡 Conseils pour de bonnes photos</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-800 mb-2">Photos de galerie :</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Format portrait (plus haut que large)</li>
                <li>• Fond neutre ou sombre</li>
                <li>• Montre bien centrée</li>
                <li>• Éclairage uniforme</li>
                <li>• Résolution minimum 800x1000px</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800 mb-2">Arrière-plan hero :</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Format paysage (plus large que haut)</li>
                <li>• Image sombre pour contraste texte</li>
                <li>• Peut être une collection de montres</li>
                <li>• Éviter les détails trop fins</li>
                <li>• Résolution minimum 1920x1080px</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
