import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST() {
  try {
    console.log('🔧 Correction des images...')

    // Images de montres qui fonctionnent
    const workingImages = [
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1533139502658-0198f920d8e8?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1508057198894-247b23fe5ade?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1609081219090-a6d81d3085bf?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=400&h=400&fit=crop'
    ]

    // Récupérer tous les produits
    const products = await prisma.product.findMany({
      include: {
        images: true
      }
    })

    let updatedCount = 0

    for (let i = 0; i < products.length; i++) {
      const product = products[i]
      const imageUrl = workingImages[i % workingImages.length]

      if (product.images.length > 0) {
        // Mettre à jour l'image existante
        await prisma.productImage.update({
          where: { id: product.images[0].id },
          data: { url: imageUrl }
        })
      } else {
        // Créer une nouvelle image
        await prisma.productImage.create({
          data: {
            productId: product.id,
            url: imageUrl,
            alt: `Photo de ${product.name}`,
            isPrimary: true
          }
        })
      }
      updatedCount++
    }

    return NextResponse.json({
      message: `${updatedCount} images corrigées avec succès`,
      updatedCount
    })

  } catch (error) {
    console.error('Erreur lors de la correction des images:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la correction des images' },
      { status: 500 }
    )
  }
}
