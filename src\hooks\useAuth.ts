'use client'

import { useState, useEffect } from 'react'

interface User {
  id: string
  firstName: string | null
  lastName: string | null
  email: string
  phone: string | null
}

// État global partagé
let globalUser: User | null = null
let listeners: ((user: User | null) => void)[] = []

// Fonction pour notifier tous les composants
const notifyListeners = (user: User | null) => {
  globalUser = user
  listeners.forEach(listener => listener(user))
}

// Charger l'utilisateur depuis localStorage au démarrage
if (typeof window !== 'undefined') {
  const savedUser = localStorage.getItem('user')
  if (savedUser) {
    try {
      globalUser = JSON.parse(savedUser)
    } catch (error) {
      console.error('Erreur lors du chargement de l\'utilisateur:', error)
      localStorage.removeItem('user')
    }
  }
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(globalUser)
  const [loading, setLoading] = useState(false)

  // S'abonner aux changements globaux
  useEffect(() => {
    listeners.push(setUser)
    return () => {
      listeners = listeners.filter(listener => listener !== setUser)
    }
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    console.log('🔄 Tentative de connexion pour:', email)
    setLoading(true)

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      })

      const result = await response.json()
      console.log('📡 Réponse du serveur:', result)

      if (response.ok) {
        const userData = result.user
        // Mettre à jour globalement
        notifyListeners(userData)
        // Sauvegarder dans localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(userData))
        }
        console.log('✅ Connexion réussie pour:', userData.email)
        return true
      } else {
        console.error('❌ Erreur de connexion:', result.error)
        return false
      }
    } catch (error) {
      console.error('❌ Erreur lors de la connexion:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    console.log('🚪 Déconnexion')
    // Mettre à jour globalement
    notifyListeners(null)
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user')
    }
  }

  console.log('🔍 useAuth - User actuel:', user)

  return {
    user,
    login,
    logout,
    loading
  }
}
