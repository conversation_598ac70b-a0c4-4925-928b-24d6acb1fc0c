'use client'

import { useState, useEffect } from 'react'

interface User {
  id: string
  firstName: string | null
  lastName: string | null
  email: string
  phone: string | null
}

// État global simple
let globalUser: User | null = null
let globalSetUser: ((user: User | null) => void) | null = null

export function useAuth() {
  const [user, setUser] = useState<User | null>(globalUser)
  const [loading, setLoading] = useState(false)

  // Synchroniser avec l'état global
  useEffect(() => {
    globalSetUser = setUser
    return () => {
      globalSetUser = null
    }
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    setLoading(true)
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      })

      const result = await response.json()

      if (response.ok) {
        const userData = result.user
        setUser(userData)
        globalUser = userData
        if (globalSetUser) globalSetUser(userData)
        // Sauvegarder dans localStorage pour persistance
        localStorage.setItem('user', JSON.stringify(userData))
        return true
      } else {
        console.error('Erreur de connexion:', result.error)
        return false
      }
    } catch (error) {
      console.error('Erreur lors de la connexion:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    globalUser = null
    if (globalSetUser) globalSetUser(null)
    localStorage.removeItem('user')
  }

  // Charger l'utilisateur depuis localStorage au démarrage
  useEffect(() => {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser)
        setUser(userData)
        globalUser = userData
      } catch (error) {
        console.error('Erreur lors du chargement de l\'utilisateur:', error)
        localStorage.removeItem('user')
      }
    }
  }, [])

  return { user, login, logout, loading }
}
