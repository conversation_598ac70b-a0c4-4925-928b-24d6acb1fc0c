import { useState, useEffect, createContext, useContext } from 'react'

interface User {
  id: string
  firstName: string | null
  lastName: string | null
  email: string
  phone: string | null
}

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  loading: boolean
}

const AuthContext = createContext<AuthContextType | null>(null)

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    // Si pas de contexte, créer un état local temporaire
    const [user, setUser] = useState<User | null>(null)
    const [loading, setLoading] = useState(false)

    const login = async (email: string, password: string): Promise<boolean> => {
      setLoading(true)
      try {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password })
        })

        const result = await response.json()

        if (response.ok) {
          setUser(result.user)
          // Sauvegarder dans localStorage pour persistance
          localStorage.setItem('user', JSON.stringify(result.user))
          return true
        } else {
          return false
        }
      } catch (error) {
        console.error('Erreur lors de la connexion:', error)
        return false
      } finally {
        setLoading(false)
      }
    }

    const logout = () => {
      setUser(null)
      localStorage.removeItem('user')
    }

    // Charger l'utilisateur depuis localStorage au démarrage
    useEffect(() => {
      const savedUser = localStorage.getItem('user')
      if (savedUser) {
        try {
          setUser(JSON.parse(savedUser))
        } catch (error) {
          console.error('Erreur lors du chargement de l\'utilisateur:', error)
          localStorage.removeItem('user')
        }
      }
    }, [])

    return { user, login, logout, loading }
  }
  return context
}
