'use client'

import { useState, useEffect } from 'react'

interface User {
  id: string
  firstName: string | null
  lastName: string | null
  email: string
  phone: string | null
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)

  // Charger l'utilisateur depuis localStorage au démarrage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedUser = localStorage.getItem('user')
      if (savedUser) {
        try {
          const userData = JSON.parse(savedUser)
          setUser(userData)
        } catch (error) {
          console.error('Erreur lors du chargement de l\'utilisateur:', error)
          localStorage.removeItem('user')
        }
      }
    }
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    console.log('🔄 Tentative de connexion pour:', email)
    setLoading(true)

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      })

      const result = await response.json()
      console.log('📡 Réponse du serveur:', result)

      if (response.ok) {
        const userData = result.user
        setUser(userData)
        // Sauvegarder dans localStorage pour persistance
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(userData))
        }
        console.log('✅ Connexion réussie pour:', userData.email)
        return true
      } else {
        console.error('❌ Erreur de connexion:', result.error)
        return false
      }
    } catch (error) {
      console.error('❌ Erreur lors de la connexion:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    console.log('🚪 Déconnexion')
    setUser(null)
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user')
    }
  }

  console.log('🔍 useAuth - User actuel:', user)
  console.log('🔍 useAuth - Login function:', typeof login)

  return {
    user,
    login,
    logout,
    loading
  }
}
