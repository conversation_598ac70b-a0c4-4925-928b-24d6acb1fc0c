import { isDatabaseConfigured } from './prisma'
import * as prismaDb from './database-prisma'
import type { FilterOptions } from '@/types'

// Mock data for when Supabase is not configured
const mockProducts = [
  {
    id: '1',
    name: 'Rolex Submariner Date',
    description: 'Montre de plongée iconique avec lunette tournante unidirectionnelle et étanchéité à 300 mètres.',
    price: 8500,
    original_price: 9000,
    category: { name: 'Luxe' },
    brand: { name: 'Rolex' },
    gender: 'homme',
    stock_quantity: 5,
    sku: 'ROL-SUB-001',
    features: ['Étanche 300m', 'Mouvement automatique', 'Acier inoxydable', 'Lunette céramique'],
    is_featured: true,
    images: [{ image_url: '/api/placeholder/300/300', alt_text: 'Rolex Submariner', is_primary: true }],
    reviews: [{ rating: 5 }, { rating: 5 }, { rating: 4 }]
  },
  {
    id: '2',
    name: 'Omega Speedmaster Professional',
    description: 'La montre lunaire légendaire, chronographe mécanique de précision.',
    price: 3200,
    category: { name: 'Sport' },
    brand: { name: 'Omega' },
    gender: 'homme',
    stock_quantity: 8,
    sku: 'OME-SPE-001',
    features: ['Chronographe', 'Mouvement manuel', 'Verre saphir', 'Fond transparent'],
    is_featured: true,
    images: [{ image_url: '/api/placeholder/300/300', alt_text: 'Omega Speedmaster', is_primary: true }],
    reviews: [{ rating: 5 }, { rating: 4 }, { rating: 5 }]
  },
  {
    id: '3',
    name: 'TAG Heuer Formula 1',
    description: 'Montre sportive inspirée de la Formule 1, robuste et élégante.',
    price: 1200,
    category: { name: 'Sport' },
    brand: { name: 'TAG Heuer' },
    gender: 'homme',
    stock_quantity: 12,
    sku: 'TAG-F1-001',
    features: ['Étanche 200m', 'Quartz', 'Lunette tournante', 'Bracelet acier'],
    is_featured: false,
    images: [{ image_url: '/api/placeholder/300/300', alt_text: 'TAG Heuer Formula 1', is_primary: true }],
    reviews: [{ rating: 4 }, { rating: 4 }, { rating: 5 }]
  },
  {
    id: '4',
    name: 'Casio G-Shock GA-2100',
    description: 'Montre ultra-résistante avec design moderne et fonctions multiples.',
    price: 120,
    category: { name: 'Sport' },
    brand: { name: 'Casio' },
    gender: 'homme',
    stock_quantity: 25,
    sku: 'CAS-GSH-001',
    features: ['Résistant aux chocs', 'Étanche 200m', 'Éclairage LED', 'Alarme'],
    is_featured: true,
    images: [{ image_url: '/api/placeholder/300/300', alt_text: 'Casio G-Shock', is_primary: true }],
    reviews: [{ rating: 5 }, { rating: 5 }, { rating: 4 }]
  }
]

const mockCategories = [
  { id: '1', name: 'Luxe' },
  { id: '2', name: 'Sport' },
  { id: '3', name: 'Casual' },
  { id: '4', name: 'Classique' }
]

const mockBrands = [
  { id: '1', name: 'Rolex' },
  { id: '2', name: 'Omega' },
  { id: '3', name: 'TAG Heuer' },
  { id: '4', name: 'Casio' },
  { id: '5', name: 'Seiko' },
  { id: '6', name: 'Citizen' }
]

// Products
export async function getProducts(filters?: FilterOptions) {
  if (isDatabaseConfigured) {
    return prismaDb.getProducts(filters)
  }

  // Fallback to mock data if database is not configured
  let filtered = [...mockProducts]

  if (filters?.search) {
    const searchLower = filters.search.toLowerCase()
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(searchLower) ||
      product.brand.name.toLowerCase().includes(searchLower)
    )
  }

  if (filters?.category) {
    filtered = filtered.filter(product => product.category.name === filters.category)
  }

  if (filters?.gender) {
    filtered = filtered.filter(product => product.gender === filters.gender)
  }

  if (filters?.minPrice) {
    filtered = filtered.filter(product => product.price >= filters.minPrice)
  }

  if (filters?.maxPrice) {
    filtered = filtered.filter(product => product.price <= filters.maxPrice)
  }

  return filtered
}

export async function getProductById(id: string) {
  if (isDatabaseConfigured) {
    return prismaDb.getProductById(id)
  }
  return mockProducts.find(p => p.id === id) || null
}

export async function getFeaturedProducts(limit = 6) {
  if (isDatabaseConfigured) {
    return prismaDb.getFeaturedProducts(limit)
  }
  return mockProducts.filter(p => p.is_featured).slice(0, limit)
}

// Categories
export async function getCategories() {
  if (isDatabaseConfigured) {
    return prismaDb.getCategories()
  }
  return mockCategories
}

// Brands
export async function getBrands() {
  if (isDatabaseConfigured) {
    return prismaDb.getBrands()
  }
  return mockBrands
}

// Cart functions
export async function getCartItems(userId: string) {
  if (isDatabaseConfigured) {
    return prismaDb.getCartItems(userId)
  }
  return []
}

export async function addToCart(userId: string, productId: string, quantity = 1) {
  if (isDatabaseConfigured) {
    return prismaDb.addToCart(userId, productId, quantity)
  }
  return false
}

export async function updateCartItemQuantity(userId: string, productId: string, quantity: number) {
  if (isDatabaseConfigured) {
    return prismaDb.updateCartItemQuantity(userId, productId, quantity)
  }
  return false
}

export async function removeFromCart(userId: string, productId: string) {
  if (isDatabaseConfigured) {
    return prismaDb.removeFromCart(userId, productId)
  }
  return false
}

export async function clearCart(userId: string) {
  if (isDatabaseConfigured) {
    return prismaDb.clearCart(userId)
  }
  return false
}

// Wishlist functions
export async function getWishlistItems(userId: string) {
  if (isDatabaseConfigured) {
    return prismaDb.getWishlistItems(userId)
  }
  return []
}

export async function addToWishlist(userId: string, productId: string) {
  if (isDatabaseConfigured) {
    return prismaDb.addToWishlist(userId, productId)
  }
  return false
}

export async function removeFromWishlist(userId: string, productId: string) {
  if (isDatabaseConfigured) {
    return prismaDb.removeFromWishlist(userId, productId)
  }
  return false
}

// Contact messages
export async function createContactMessage(messageData: {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
}) {
  if (isDatabaseConfigured) {
    return prismaDb.createContactMessage(messageData)
  }
  return false
}

// Discount codes
export async function validateDiscountCode(code: string, orderAmount: number) {
  if (isDatabaseConfigured) {
    return prismaDb.validateDiscountCode(code, orderAmount)
  }
  return { valid: false, error: 'Base de données non configurée' }
}












