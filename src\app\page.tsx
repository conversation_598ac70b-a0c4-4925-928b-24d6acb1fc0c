import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import DatabaseStatus from '@/components/DatabaseStatus'
import WatchImage from '@/components/ui/WatchImage'
import ImageCarousel from '@/components/ui/ImageCarousel'
import { Clock, Shield, Truck, Star } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Database Status */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
        <DatabaseStatus />
      </div>

      {/* Hero Section - Carrousel */}
      <section className="relative min-h-screen flex items-center justify-center text-white overflow-hidden">
        {/* Carrousel d'images */}
        <ImageCarousel
          images={[
            '/images/watch-1.jpg',
            '/images/watch-2.jpg',
            '/images/watch-3.jpg',
            '/images/watch-4.jpg',
            '/images/watch-5.jpg',
            '/images/watch-6.jpg'
          ]}
          fallbackImages={[
            'https://images.unsplash.com/photo-1594534475808-b18fc33b045e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
            'https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
            'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
            'https://images.unsplash.com/photo-1548181048-dcea1ba611c9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
            'https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
            'https://images.unsplash.com/photo-1524592094714-0f0654e20314?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'
          ]}
          interval={3000}
        />

        {/* Contenu principal */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white drop-shadow-2xl">
            Bienvenue chez <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Oussama Watches</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-white drop-shadow-lg max-w-4xl mx-auto">
            Votre collection de montres de luxe, sport et casual pour toute la famille
          </p>
          <p className="text-lg mb-12 text-gray-200 drop-shadow-md max-w-3xl mx-auto">
            Découvrez notre sélection exclusive de montres de qualité, alliant style, précision et élégance intemporelle.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/montres"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Voir le Catalogue
            </Link>
            <Link
              href="/montres?featured=true"
              className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg backdrop-blur-sm"
            >
              Nouveautés
            </Link>
          </div>


        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="bg-blue-100 w-20 h-20 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                <Truck className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-blue-600 transition-colors">
                Livraison Gratuite
              </h3>
              <p className="text-gray-600">
                Livraison gratuite dès <span className="font-semibold text-blue-600">1000 DH</span> d'achat
              </p>
            </div>

            <div className="text-center group">
              <div className="bg-blue-100 w-20 h-20 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                <Shield className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-blue-600 transition-colors">
                Garantie 2 ans
              </h3>
              <p className="text-gray-600">
                Toutes nos montres sont <span className="font-semibold text-blue-600">garanties</span>
              </p>
            </div>

            <div className="text-center group">
              <div className="bg-blue-100 w-20 h-20 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                <Clock className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-blue-600 transition-colors">
                Service Rapide
              </h3>
              <p className="text-gray-600">
                Expédition sous <span className="font-semibold text-blue-600">24h</span>
              </p>
            </div>

            <div className="text-center group">
              <div className="bg-blue-100 w-20 h-20 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                <Star className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-blue-600 transition-colors">
                Qualité Premium
              </h3>
              <p className="text-gray-600">
                Sélection de <span className="font-semibold text-blue-600">marques reconnues</span>
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Notre Collection</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez notre sélection de montres d'exception, alliant élégance, précision et style intemporel
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Photo 1 - Montre de Luxe */}
            <div className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
              <div className="aspect-w-4 aspect-h-5 bg-gradient-to-br from-gray-900 to-gray-700">
                <WatchImage
                  src="/images/watch-luxury.jpg"
                  fallbackSrc="https://images.unsplash.com/photo-1594534475808-b18fc33b045e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Montre de Luxe"
                  className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">Montres de Luxe</h3>
                  <p className="text-gray-200">Élégance et prestige</p>
                </div>
                <div className="absolute top-6 right-6 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Premium
                </div>
              </div>
            </div>

            {/* Photo 2 - Montre Sport */}
            <div className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
              <div className="aspect-w-4 aspect-h-5 bg-gradient-to-br from-blue-900 to-blue-700">
                <WatchImage
                  src="/images/watch-sport.jpg"
                  fallbackSrc="https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Montre Sport"
                  className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">Montres Sport</h3>
                  <p className="text-gray-200">Performance et robustesse</p>
                </div>
                <div className="absolute top-6 right-6 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Sport
                </div>
              </div>
            </div>

            {/* Photo 3 - Montre Classique */}
            <div className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
              <div className="aspect-w-4 aspect-h-5 bg-gradient-to-br from-amber-900 to-amber-700">
                <WatchImage
                  src="/images/watch-classic.jpg"
                  fallbackSrc="https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Montre Classique"
                  className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">Montres Classiques</h3>
                  <p className="text-gray-200">Intemporel et raffiné</p>
                </div>
                <div className="absolute top-6 right-6 bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Classique
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <Link
              href="/montres"
              className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Voir Toute la Collection
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
