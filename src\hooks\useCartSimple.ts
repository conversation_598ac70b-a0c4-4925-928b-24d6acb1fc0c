'use client'

import { useState, useEffect } from 'react'
import { getCurrentUser } from '@/lib/simpleAuth'

export function useCart() {
  const [cartItems, setCartItems] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    // Charger l'utilisateur et écouter les changements
    const currentUser = getCurrentUser()
    setUser(currentUser)
    
    if (currentUser) {
      loadCartItems(currentUser.id)
    } else {
      setCartItems([])
    }

    const handleUserChange = (event: any) => {
      const newUser = event.detail
      setUser(newUser)
      if (newUser) {
        loadCartItems(newUser.id)
      } else {
        setCartItems([])
      }
    }
    
    window.addEventListener('userChanged', handleUserChange)
    return () => window.removeEventListener('userChanged', handleUserChange)
  }, [])

  const loadCartItems = async (userId: string) => {
    setLoading(true)
    try {
      console.log('🛒 Chargement du panier pour userId:', userId)
      const response = await fetch(`/api/cart?userId=${userId}`)
      const data = await response.json()
      
      console.log('🛒 Réponse API cart:', data)
      
      if (response.ok) {
        setCartItems(data.cartItems || [])
        console.log('🛒 Articles chargés:', data.cartItems?.length || 0)
      } else {
        console.error('❌ Erreur lors du chargement du panier:', data.error)
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement du panier:', error)
    } finally {
      setLoading(false)
    }
  }

  const addToCart = async (productId: string, quantity = 1) => {
    if (!user) {
      alert('Vous devez être connecté pour ajouter des produits au panier')
      return false
    }

    console.log('🛒 Ajout au panier - userId:', user.id, 'productId:', productId)
    setLoading(true)
    
    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id, productId, quantity })
      })

      const data = await response.json()
      console.log('🛒 Réponse ajout au panier:', data)

      if (response.ok) {
        // Recharger le panier
        await loadCartItems(user.id)
        console.log('✅ Produit ajouté avec succès')
        return true
      } else {
        console.error('❌ Erreur lors de l\'ajout au panier:', data.error)
        alert(`Erreur: ${data.error}`)
        return false
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'ajout au panier:', error)
      alert('Erreur lors de l\'ajout au panier')
      return false
    } finally {
      setLoading(false)
    }
  }

  const getTotal = () => {
    return cartItems.reduce((total, item) => total + (item.product?.price || 0) * item.quantity, 0)
  }

  const getItemCount = () => {
    return cartItems.reduce((count, item) => count + item.quantity, 0)
  }

  return {
    cartItems,
    loading,
    addToCart,
    getTotal,
    getItemCount,
    loadCartItems: () => user ? loadCartItems(user.id) : Promise.resolve()
  }
}
