const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createUser() {
  try {
    // Données du nouvel utilisateur - MODIFIEZ AVEC VOS VRAIES INFORMATIONS
    const userData = {
      firstName: 'Oussama',           // ← Votre prénom
      lastName: 'Hami',               // ← Votre nom
      email: '<EMAIL>',  // ← Votre email
      password: 'monMotDePasse123',   // ← Votre mot de passe
      phone: '0603357867'             // ← Votre téléphone
    }

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email }
    })

    if (existingUser) {
      console.log('❌ Un utilisateur avec cet email existe déjà')
      return
    }

    // Hasher le mot de passe
    const hashedPassword = await bcrypt.hash(userData.password, 12)

    // C<PERSON>er l'utilisateur
    const user = await prisma.user.create({
      data: {
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        password: hashedPassword,
        phone: userData.phone,
        role: 'USER'
      }
    })

    console.log('✅ Utilisateur créé avec succès:')
    console.log(`📧 Email: ${user.email}`)
    console.log(`👤 Nom: ${user.firstName} ${user.lastName}`)
    console.log(`📱 Téléphone: ${user.phone}`)
    console.log(`🆔 ID: ${user.id}`)

  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'utilisateur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createUser()
