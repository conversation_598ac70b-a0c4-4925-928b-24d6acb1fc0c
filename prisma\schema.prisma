// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?   @db.Text
  imageUrl    String?   @map("image_url")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  products    Product[]
  
  @@map("categories")
}

model Brand {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?   @db.Text
  logoUrl     String?   @map("logo_url")
  websiteUrl  String?   @map("website_url")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  products    Product[]
  
  @@map("brands")
}

model Product {
  id              String    @id @default(cuid())
  name            String
  description     String?   @db.Text
  price           Decimal   @db.Decimal(10, 2)
  originalPrice   Decimal?  @map("original_price") @db.Decimal(10, 2)
  categoryId      String    @map("category_id")
  brandId         String    @map("brand_id")
  gender          Gender
  stockQuantity   Int       @default(0) @map("stock_quantity")
  minStockLevel   Int       @default(5) @map("min_stock_level")
  sku             String    @unique
  weight          Decimal?  @db.Decimal(8, 2)
  features        Json?
  isActive        Boolean   @default(true) @map("is_active")
  isFeatured      Boolean   @default(false) @map("is_featured")
  metaTitle       String?   @map("meta_title")
  metaDescription String?   @map("meta_description") @db.Text
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  
  category        Category  @relation(fields: [categoryId], references: [id])
  brand           Brand     @relation(fields: [brandId], references: [id])
  images          ProductImage[]
  reviews         ProductReview[]
  cartItems       CartItem[]
  wishlistItems   WishlistItem[]
  orderItems      OrderItem[]
  
  @@map("products")
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String   @map("product_id")
  imageUrl  String   @map("image_url")
  altText   String?  @map("alt_text")
  sortOrder Int      @default(0) @map("sort_order")
  isPrimary Boolean  @default(false) @map("is_primary")
  createdAt DateTime @default(now()) @map("created_at")
  
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@map("product_images")
}

model User {
  id          String    @id @default(cuid())
  email       String    @unique
  password    String
  firstName   String?   @map("first_name")
  lastName    String?   @map("last_name")
  phone       String?
  dateOfBirth DateTime? @map("date_of_birth")
  gender      String?
  avatarUrl   String?   @map("avatar_url")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  
  addresses     Address[]
  orders        Order[]
  cartItems     CartItem[]
  wishlistItems WishlistItem[]
  reviews       ProductReview[]
  
  @@map("users")
}

model Address {
  id            String      @id @default(cuid())
  userId        String      @map("user_id")
  type          AddressType @default(BOTH)
  firstName     String?     @map("first_name")
  lastName      String?     @map("last_name")
  company       String?
  streetAddress String      @map("street_address") @db.Text
  city          String
  stateProvince String?     @map("state_province")
  postalCode    String      @map("postal_code")
  country       String      @default("Maroc")
  phone         String?
  isDefault     Boolean     @default(false) @map("is_default")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")
  
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("addresses")
}

model Order {
  id              String        @id @default(cuid())
  userId          String        @map("user_id")
  orderNumber     String        @unique @map("order_number")
  status          OrderStatus   @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING) @map("payment_status")
  paymentMethod   PaymentMethod? @map("payment_method")
  paymentIntentId String?       @map("payment_intent_id")
  subtotal        Decimal       @db.Decimal(10, 2)
  discountAmount  Decimal       @default(0) @map("discount_amount") @db.Decimal(10, 2)
  shippingCost    Decimal       @default(0) @map("shipping_cost") @db.Decimal(10, 2)
  taxAmount       Decimal       @default(0) @map("tax_amount") @db.Decimal(10, 2)
  totalAmount     Decimal       @map("total_amount") @db.Decimal(10, 2)
  currency        String        @default("EUR")
  shippingAddress Json?         @map("shipping_address")
  billingAddress  Json?         @map("billing_address")
  notes           String?       @db.Text
  shippedAt       DateTime?     @map("shipped_at")
  deliveredAt     DateTime?     @map("delivered_at")
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  
  user            User          @relation(fields: [userId], references: [id])
  items           OrderItem[]
  discounts       OrderDiscount[]
  
  @@map("orders")
}

model OrderItem {
  id          String  @id @default(cuid())
  orderId     String  @map("order_id")
  productId   String  @map("product_id")
  productName String  @map("product_name")
  productSku  String? @map("product_sku")
  quantity    Int
  unitPrice   Decimal @map("unit_price") @db.Decimal(10, 2)
  totalPrice  Decimal @map("total_price") @db.Decimal(10, 2)
  createdAt   DateTime @default(now()) @map("created_at")
  
  order       Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product     Product @relation(fields: [productId], references: [id])
  
  @@map("order_items")
}

model CartItem {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  productId String   @map("product_id")
  quantity  Int      @default(1)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@unique([userId, productId])
  @@map("cart_items")
}

model WishlistItem {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  productId String   @map("product_id")
  createdAt DateTime @default(now()) @map("created_at")
  
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@unique([userId, productId])
  @@map("wishlist_items")
}

model ProductReview {
  id                 String   @id @default(cuid())
  productId          String   @map("product_id")
  userId             String   @map("user_id")
  orderId            String?  @map("order_id")
  rating             Int
  title              String?
  comment            String?  @db.Text
  isVerifiedPurchase Boolean  @default(false) @map("is_verified_purchase")
  isApproved         Boolean  @default(false) @map("is_approved")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")
  
  product            Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([productId, userId])
  @@map("product_reviews")
}

model DiscountCode {
  id                    String    @id @default(cuid())
  code                  String    @unique
  description           String?   @db.Text
  type                  DiscountType
  value                 Decimal   @db.Decimal(10, 2)
  minimumOrderAmount    Decimal?  @map("minimum_order_amount") @db.Decimal(10, 2)
  maximumDiscountAmount Decimal?  @map("maximum_discount_amount") @db.Decimal(10, 2)
  usageLimit            Int?      @map("usage_limit")
  usedCount             Int       @default(0) @map("used_count")
  isActive              Boolean   @default(true) @map("is_active")
  startsAt              DateTime? @map("starts_at")
  expiresAt             DateTime? @map("expires_at")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  
  orderDiscounts        OrderDiscount[]
  
  @@map("discount_codes")
}

model OrderDiscount {
  id             String       @id @default(cuid())
  orderId        String       @map("order_id")
  discountCodeId String       @map("discount_code_id")
  discountAmount Decimal      @map("discount_amount") @db.Decimal(10, 2)
  createdAt      DateTime     @default(now()) @map("created_at")
  
  order          Order        @relation(fields: [orderId], references: [id], onDelete: Cascade)
  discountCode   DiscountCode @relation(fields: [discountCodeId], references: [id])
  
  @@map("order_discounts")
}

model ContactMessage {
  id         String            @id @default(cuid())
  name       String
  email      String
  phone      String?
  subject    String
  message    String            @db.Text
  status     ContactStatus     @default(NEW)
  adminNotes String?           @map("admin_notes") @db.Text
  createdAt  DateTime          @default(now()) @map("created_at")
  updatedAt  DateTime          @updatedAt @map("updated_at")
  
  @@map("contact_messages")
}

// Enums
enum Gender {
  homme
  femme
  enfant
  unisexe
}

enum AddressType {
  BILLING
  SHIPPING
  BOTH
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PaymentMethod {
  STRIPE
  PAYPAL
  CASH_ON_DELIVERY
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum ContactStatus {
  NEW
  IN_PROGRESS
  RESOLVED
  CLOSED
}
