const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function listUsers() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        role: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    console.log('📋 Liste de tous les utilisateurs:')
    console.log('=====================================')
    
    if (users.length === 0) {
      console.log('❌ Aucun utilisateur trouvé')
    } else {
      users.forEach((user, index) => {
        console.log(`\n👤 Utilisateur ${index + 1}:`)
        console.log(`🆔 ID: ${user.id}`)
        console.log(`📧 Email: ${user.email}`)
        console.log(`👤 Nom: ${user.firstName} ${user.lastName}`)
        console.log(`📱 Téléphone: ${user.phone || 'Non renseigné'}`)
        console.log(`🔑 Rôle: ${user.role}`)
        console.log(`📅 Créé le: ${user.createdAt.toLocaleString('fr-FR')}`)
        console.log('---')
      })
    }

    console.log(`\n📊 Total: ${users.length} utilisateur(s)`)

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error)
  } finally {
    await prisma.$disconnect()
  }
}

listUsers()
