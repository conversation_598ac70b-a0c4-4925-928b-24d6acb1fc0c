import { prisma } from './prisma'
import bcrypt from 'bcryptjs'
import { SignJWT, jwtVerify } from 'jose'

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-change-this-in-production'
)

export interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
}

export async function signUp(email: string, password: string, userData?: {
  firstName?: string
  lastName?: string
  phone?: string
}) {
  try {
    // Vérifier si l'utilisateur existe déjà
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return { error: { message: 'Un compte avec cet email existe déjà' } }
    }

    // Hacher le mot de passe
    const hashedPassword = await bcrypt.hash(password, 10)

    // Créer l'utilisateur
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        firstName: userData?.firstName,
        lastName: userData?.lastName,
        phone: userData?.phone
      }
    })

    // Créer le token JWT
    const token = await new SignJWT({ userId: user.id, email: user.email })
      .setProtectedHeader({ alg: 'HS256' })
      .setExpirationTime('7d')
      .sign(JWT_SECRET)

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName
      },
      token
    }
  } catch (error) {
    console.error('Error signing up:', error)
    return { error: { message: 'Erreur lors de la création du compte' } }
  }
}

export async function signIn(email: string, password: string) {
  try {
    // Trouver l'utilisateur
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      return { error: { message: 'Email ou mot de passe incorrect' } }
    }

    // Vérifier le mot de passe
    const isValidPassword = await bcrypt.compare(password, user.password)

    if (!isValidPassword) {
      return { error: { message: 'Email ou mot de passe incorrect' } }
    }

    // Créer le token JWT
    const token = await new SignJWT({ userId: user.id, email: user.email })
      .setProtectedHeader({ alg: 'HS256' })
      .setExpirationTime('7d')
      .sign(JWT_SECRET)

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName
      },
      token
    }
  } catch (error) {
    console.error('Error signing in:', error)
    return { error: { message: 'Erreur lors de la connexion' } }
  }
}

export async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    
    // Vérifier que l'utilisateur existe toujours
    const user = await prisma.user.findUnique({
      where: { id: payload.userId as string }
    })

    if (!user) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName
    }
  } catch (error) {
    console.error('Error verifying token:', error)
    return null
  }
}

export async function getUserProfile(userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        dateOfBirth: true,
        gender: true,
        avatarUrl: true,
        createdAt: true
      }
    })

    return user
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
}

export async function updateUserProfile(userId: string, profileData: any) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: profileData
    })
    return true
  } catch (error) {
    console.error('Error updating user profile:', error)
    return false
  }
}
