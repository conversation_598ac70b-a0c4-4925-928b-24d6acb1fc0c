import Link from 'next/link'
import { Mail, Phone, MapPin } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
      {/* Éléments décoratifs */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-float"></div>
      <div className="absolute bottom-0 right-0 w-64 h-64 bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-float animation-delay-400"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="group">
            <div className="mb-6">
              <h3 className="text-3xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Oussama Watches
              </h3>
              <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Votre spécialiste en montres de qualité à Casablanca.
              Découvrez notre sélection exclusive de montres pour hommes.
            </p>
            <div className="space-y-3 mb-6">
              <div className="flex items-center text-sm text-gray-300 group-hover:text-green-400 transition-colors">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></div>
                <span>Garantie authentique sur tous nos produits</span>
              </div>
              <div className="flex items-center text-sm text-gray-300 group-hover:text-blue-400 transition-colors">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3 animate-pulse animation-delay-200"></div>
                <span>Livraison rapide dans tout le Maroc</span>
              </div>
              <div className="flex items-center text-sm text-gray-300 group-hover:text-purple-400 transition-colors">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-3 animate-pulse animation-delay-400"></div>
                <span>Service client personnalisé</span>
              </div>
            </div>
            <div className="flex space-x-3">
              <a
                href="https://wa.me/212603357867"
                target="_blank"
                rel="noopener noreferrer"
                className="group relative p-3 bg-gray-800 rounded-xl hover:bg-green-600 transition-all duration-300 transform hover:scale-110 hover:shadow-lg"
                title="WhatsApp"
              >
                <svg className="h-5 w-5 text-gray-300 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
                <div className="absolute inset-0 bg-green-400 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity"></div>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="group relative p-3 bg-gray-800 rounded-xl hover:bg-blue-600 transition-all duration-300 transform hover:scale-110 hover:shadow-lg"
                title="Email"
              >
                <Mail className="h-5 w-5 text-gray-300 group-hover:text-white transition-colors" />
                <div className="absolute inset-0 bg-blue-400 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity"></div>
              </a>
              <a
                href="tel:+212603357867"
                className="group relative p-3 bg-gray-800 rounded-xl hover:bg-purple-600 transition-all duration-300 transform hover:scale-110 hover:shadow-lg"
                title="Téléphone"
              >
                <Phone className="h-5 w-5 text-gray-300 group-hover:text-white transition-colors" />
                <div className="absolute inset-0 bg-purple-400 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity"></div>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-semibold mb-6 text-white">Liens rapides</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/montres" className="group flex items-center text-gray-300 hover:text-blue-400 transition-all duration-300">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Catalogue
                </Link>
              </li>
              <li>
                <Link href="/montres?category=Luxe" className="group flex items-center text-gray-300 hover:text-blue-400 transition-all duration-300">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Montres de Luxe
                </Link>
              </li>
              <li>
                <Link href="/montres?category=Sport" className="group flex items-center text-gray-300 hover:text-blue-400 transition-all duration-300">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Montres Sport
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h4 className="text-xl font-semibold mb-6 text-white">Service Client</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/contact" className="group flex items-center text-gray-300 hover:text-green-400 transition-all duration-300">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Nous contacter
                </Link>
              </li>
              <li>
                <Link href="/livraison" className="group flex items-center text-gray-300 hover:text-green-400 transition-all duration-300">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Livraison
                </Link>
              </li>
              <li>
                <Link href="/retours" className="group flex items-center text-gray-300 hover:text-green-400 transition-all duration-300">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Retours
                </Link>
              </li>
              <li>
                <Link href="/garantie" className="group flex items-center text-gray-300 hover:text-green-400 transition-all duration-300">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Garantie
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-semibold mb-6 text-white">Contact</h4>
            <div className="space-y-4">
              <div className="group flex items-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-300">
                <div className="p-2 bg-blue-600 rounded-lg mr-3 group-hover:scale-110 transition-transform">
                  <Phone className="h-4 w-4 text-white" />
                </div>
                <span className="text-gray-300 group-hover:text-white transition-colors">0603357867</span>
              </div>
              <div className="group flex items-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-300">
                <div className="p-2 bg-purple-600 rounded-lg mr-3 group-hover:scale-110 transition-transform">
                  <Mail className="h-4 w-4 text-white" />
                </div>
                <span className="text-gray-300 group-hover:text-white transition-colors text-sm"><EMAIL></span>
              </div>
              <div className="group flex items-center p-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-300">
                <div className="p-2 bg-green-600 rounded-lg mr-3 group-hover:scale-110 transition-transform">
                  <MapPin className="h-4 w-4 text-white" />
                </div>
                <span className="text-gray-300 group-hover:text-white transition-colors">Casablanca - Sidi Bernoussi</span>
              </div>
            </div>

            <div className="mt-8 p-4 bg-gradient-to-r from-green-800 to-green-700 rounded-xl border border-green-600">
              <h4 className="text-lg font-semibold mb-4 text-white flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                Disponibilité
              </h4>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center p-3 bg-green-700 rounded-lg border border-green-500">
                  <span className="text-green-100 font-medium">Lundi - Dimanche:</span>
                  <span className="text-green-200 font-bold text-lg">24h/24</span>
                </div>
                <div className="text-center p-3 bg-green-600 rounded-lg">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-3 h-3 bg-green-300 rounded-full animate-pulse"></div>
                    <span className="text-green-100 font-semibold">Service disponible 7j/7</span>
                    <div className="w-3 h-3 bg-green-300 rounded-full animate-pulse animation-delay-200"></div>
                  </div>
                  <p className="text-green-200 text-xs mt-2">
                    Contactez-nous à tout moment !
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter */}
        <div className="border-t border-gray-700 mt-12 pt-12">
          <div className="max-w-lg mx-auto text-center">
            <div className="bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl p-8 shadow-xl">
              <h4 className="text-2xl font-bold mb-3 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Newsletter
              </h4>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Recevez nos dernières offres et nouveautés directement dans votre boîte mail
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <input
                  type="email"
                  placeholder="Votre email"
                  className="flex-1 px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 text-white placeholder-gray-400 transition-all input-black-text"
                />
                <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                  S'abonner
                </button>
              </div>
              <p className="text-xs text-gray-400 mt-4">
                🔒 Vos données sont protégées et ne seront jamais partagées
              </p>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 pt-8 mt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
            <div className="text-center lg:text-left">
              <p className="text-gray-300 text-lg font-medium">
                © 2024 <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent font-bold">Oussama Watches</span>. Tous droits réservés.
              </p>
              <p className="text-gray-400 text-sm mt-1">
                Fait avec ❤️ à Casablanca
              </p>
            </div>

            {/* Payment Methods */}
            <div className="flex flex-col items-center lg:items-end">
              <span className="text-gray-400 text-sm mb-3 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                Paiement 100% sécurisé
              </span>
              <div className="flex space-x-3">
                <div className="group bg-white rounded-lg px-3 py-2 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <span className="text-blue-600 font-bold text-sm group-hover:text-blue-700">VISA</span>
                </div>
                <div className="group bg-white rounded-lg px-3 py-2 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <span className="text-red-600 font-bold text-sm group-hover:text-red-700">MC</span>
                </div>
                <div className="group bg-white rounded-lg px-3 py-2 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <span className="text-blue-800 font-bold text-sm group-hover:text-blue-900">PayPal</span>
                </div>
                <div className="group bg-white rounded-lg px-3 py-2 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <span className="text-purple-600 font-bold text-sm group-hover:text-purple-700">Stripe</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
