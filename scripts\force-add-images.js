const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function forceAddImages() {
  try {
    // Supprimer toutes les images existantes
    await prisma.productImage.deleteMany({})
    console.log('🗑️ Anciennes images supprimées')

    // Récupérer tous les produits
    const products = await prisma.product.findMany()
    console.log(`📦 ${products.length} produits trouvés`)

    // Images de montres de haute qualité
    const watchImages = [
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500&h=500&fit=crop&crop=center',
      'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=500&h=500&fit=crop&crop=center',
      'https://images.unsplash.com/photo-1533139502658-0198f920d8e8?w=500&h=500&fit=crop&crop=center',
      'https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?w=500&h=500&fit=crop&crop=center',
      'https://images.unsplash.com/photo-1508057198894-247b23fe5ade?w=500&h=500&fit=crop&crop=center',
      'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=500&h=500&fit=crop&crop=center',
      'https://images.unsplash.com/photo-1609081219090-a6d81d3085bf?w=500&h=500&fit=crop&crop=center',
      'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=500&h=500&fit=crop&crop=center'
    ]

    // Ajouter une image à chaque produit
    for (let i = 0; i < products.length; i++) {
      const product = products[i]
      const imageUrl = watchImages[i % watchImages.length]
      
      await prisma.productImage.create({
        data: {
          productId: product.id,
          url: imageUrl,
          alt: `Photo de ${product.name}`,
          isPrimary: true
        }
      })
      
      console.log(`✅ Image ajoutée pour: ${product.name} - ${imageUrl}`)
    }

    console.log('🎉 Toutes les images ont été forcées !')

    // Vérifier le résultat
    const productsWithImages = await prisma.product.findMany({
      include: {
        images: true
      }
    })

    console.log('\n📊 Résultat final:')
    productsWithImages.forEach(product => {
      console.log(`- ${product.name}: ${product.images.length} image(s)`)
    })

  } catch (error) {
    console.error('❌ Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

forceAddImages()
