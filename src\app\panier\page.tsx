'use client'

import { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Minus, Plus, Trash2, ShoppingBag, ArrowLeft, CreditCard } from 'lucide-react'
import { formatPrice } from '@/lib/utils'

// Mock cart data
const initialCartItems = [
  {
    id: '1',
    name: 'Rolex Submariner',
    brand: 'Rolex',
    price: 8500,
    quantity: 1,
    image: '/api/placeholder/150/150',
    inStock: true
  },
  {
    id: '2',
    name: 'Omega Speedmaster',
    brand: 'Omega',
    price: 3200,
    quantity: 2,
    image: '/api/placeholder/150/150',
    inStock: true
  }
]

export default function PanierPage() {
  const [cartItems, setCartItems] = useState(initialCartItems)

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity === 0) {
      removeItem(id)
      return
    }
    
    setCartItems(items =>
      items.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      )
    )
  }

  const removeItem = (id: string) => {
    setCartItems(items => items.filter(item => item.id !== id))
  }



  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  const shipping = subtotal > 1000 ? 0 : 50 // Free shipping over 1000 DH
  const total = subtotal + shipping

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <ShoppingBag className="h-24 w-24 text-gray-400 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Votre panier est vide</h1>
            <p className="text-gray-600 mb-8">
              Découvrez notre collection de montres et ajoutez vos favoris au panier
            </p>
            <Link
              href="/montres"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Continuer les achats
            </Link>
          </div>
        </div>

        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-4">
            Panier d'achat
          </h1>
          <p className="text-xl text-gray-600 font-light">
            {cartItems.length} article{cartItems.length > 1 ? 's' : ''} dans votre panier
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mt-4 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white border border-gray-100 rounded-2xl shadow-xl overflow-hidden backdrop-blur-sm">
              <div className="p-8">
                <div className="space-y-8">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex items-center space-x-6 pb-8 border-b border-gray-100 last:border-b-0 last:pb-0 group hover:bg-gray-50 rounded-xl p-4 transition-all duration-300">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                          <span className="text-sm text-gray-600 text-center font-medium">
                            {item.name}
                          </span>
                        </div>
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">{item.name}</h3>
                        <p className="text-lg text-gray-500 font-medium mb-2">{item.brand}</p>
                        <p className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          {formatPrice(item.price)}
                        </p>
                        {!item.inStock && (
                          <p className="text-sm text-red-500 mt-2 font-medium">Produit indisponible</p>
                        )}
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-4 bg-gray-50 rounded-xl p-3">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-2 rounded-full bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
                          disabled={!item.inStock}
                        >
                          <Minus className="h-4 w-4 text-gray-600" />
                        </button>
                        <span className="w-16 text-center font-bold text-xl text-gray-900">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-2 rounded-full bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
                          disabled={!item.inStock}
                        >
                          <Plus className="h-4 w-4 text-gray-600" />
                        </button>
                      </div>

                      {/* Total Price */}
                      <div className="text-right">
                        <p className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                          {formatPrice(item.price * item.quantity)}
                        </p>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeItem(item.id)}
                        className="p-3 text-red-500 hover:bg-red-50 hover:text-red-600 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md"
                      >
                        <Trash2 className="h-6 w-6" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Continue Shopping */}
            <div className="mt-8">
              <Link
                href="/montres"
                className="inline-flex items-center bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-800 px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
              >
                <ArrowLeft className="h-5 w-5 mr-3" />
                Continuer les achats
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-100 rounded-2xl p-8 sticky top-4 shadow-2xl backdrop-blur-sm">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-8 text-center">
                Résumé de la commande
              </h2>
              


              {/* Price Breakdown */}
              <div className="space-y-4 mb-8 bg-white rounded-xl p-6 shadow-sm">
                <div className="flex justify-between text-lg">
                  <span className="text-gray-700 font-medium">Sous-total</span>
                  <span className="font-bold text-gray-900">{formatPrice(subtotal)}</span>
                </div>

                <div className="flex justify-between text-lg">
                  <span className="text-gray-700 font-medium">Livraison</span>
                  <span className="font-bold text-gray-900">
                    {shipping === 0 ? 'Gratuite' : formatPrice(shipping)}
                  </span>
                </div>

                {shipping === 0 && subtotal > 1000 && (
                  <p className="text-sm text-green-600 bg-green-50 p-2 rounded-lg font-medium">
                    🚚 Livraison gratuite dès 1000 DH d'achat
                  </p>
                )}

                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between text-2xl font-bold">
                    <span className="text-gray-900">Total</span>
                    <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">{formatPrice(total)}</span>
                  </div>
                </div>
              </div>

              {/* Checkout Button */}
              <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 px-6 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                <CreditCard className="h-6 w-6 mr-3" />
                Procéder au paiement
              </button>


            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
