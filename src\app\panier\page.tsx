'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Minus, Plus, Trash2, ShoppingBag, ArrowLeft, CreditCard } from 'lucide-react'
import { formatPrice } from '@/lib/utils'
import { ADMIN_CONFIG } from '@/config/admin'
import { getCurrentUser } from '@/lib/simpleAuth'

export default function PanierPage() {
  const [cartItems, setCartItems] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)

  // Charger l'utilisateur et le panier
  useEffect(() => {
    const currentUser = getCurrentUser()
    setUser(currentUser)

    if (currentUser) {
      loadCartItems(currentUser.id)
    } else {
      setLoading(false)
    }
  }, [])

  // Fonction pour charger les articles du panier
  const loadCartItems = async (userId: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/cart?userId=${userId}`)
      const data = await response.json()

      if (response.ok) {
        setCartItems(data.cartItems || [])
      } else {
        console.error('Erreur lors du chargement du panier:', data.error)
      }
    } catch (error) {
      console.error('Erreur lors du chargement du panier:', error)
    } finally {
      setLoading(false)
    }
  }
  const [showCheckoutForm, setShowCheckoutForm] = useState(false)
  const [formData, setFormData] = useState({
    nom: '',
    telephone: '',
    ville: '',
    adresse: ''
  })

  const updateQuantity = async (cartItemId: string, newQuantity: number) => {
    if (newQuantity === 0) {
      removeItem(cartItemId)
      return
    }

    try {
      const response = await fetch('/api/cart', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cartItemId, quantity: newQuantity })
      })

      if (response.ok) {
        // Recharger le panier
        if (user) {
          loadCartItems(user.id)
        }
      } else {
        const data = await response.json()
        alert(`Erreur: ${data.error}`)
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error)
      alert('Erreur lors de la mise à jour')
    }
  }

  const removeItem = async (cartItemId: string) => {
    try {
      const response = await fetch(`/api/cart?cartItemId=${cartItemId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        // Recharger le panier
        if (user) {
          loadCartItems(user.id)
        }
      } else {
        const data = await response.json()
        alert(`Erreur: ${data.error}`)
      }
    } catch (error) {
      console.error('Erreur lors de la suppression:', error)
      alert('Erreur lors de la suppression')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleCheckout = () => {
    setShowCheckoutForm(true)
  }

  const sendWhatsAppToAdmin = (orderData: any) => {
    // Message très simple sans caractères spéciaux
    const message = `Bonjour Oussama Watches! Nouvelle commande: ${orderData.nom}, Tel: ${orderData.telephone}, Ville: ${orderData.ville}, Adresse: ${orderData.adresse}, Total: ${formatPrice(total)}`

    // Créer l'URL WhatsApp vers votre numéro
    const whatsappUrl = `https://wa.me/212603357867?text=${encodeURIComponent(message)}`

    // Debug - afficher l'URL dans la console
    console.log('📱 URL WhatsApp:', whatsappUrl)
    console.log('📱 Message:', message)

    // Ouvrir WhatsApp pour que le client envoie le message
    window.open(whatsappUrl, '_blank')
  }

  const handleSubmitOrder = (e: React.FormEvent) => {
    e.preventDefault()

    // Validation simple
    if (!formData.nom || !formData.telephone || !formData.ville || !formData.adresse) {
      alert('Veuillez remplir tous les champs')
      return
    }

    // Ouvrir WhatsApp pour que le client envoie le message à l'admin
    sendWhatsAppToAdmin(formData)

    // Confirmation pour le client
    alert(`✅ Commande préparée pour ${formData.nom}!\n\n💰 Total: ${formatPrice(total)}\n📍 Livraison à: ${formData.adresse}, ${formData.ville}\n\n📱 WhatsApp va s'ouvrir pour envoyer votre commande à notre équipe.`)

    // Reset
    setShowCheckoutForm(false)
    setFormData({ nom: '', telephone: '', ville: '', adresse: '' })
    setCartItems([])
  }



  const subtotal = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0)
  const shipping = subtotal > 1000 ? 0 : 50 // Free shipping over 1000 DH
  const total = subtotal + shipping

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement du panier...</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <ShoppingBag className="h-24 w-24 text-gray-400 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Connexion requise</h1>
            <p className="text-gray-600 mb-8">
              Vous devez être connecté pour voir votre panier
            </p>
            <Link
              href="/compte"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Se connecter
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <ShoppingBag className="h-24 w-24 text-gray-400 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Votre panier est vide</h1>
            <p className="text-gray-600 mb-8">
              Découvrez notre collection de montres et ajoutez vos favoris au panier
            </p>
            <Link
              href="/montres"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Continuer les achats
            </Link>
          </div>
        </div>

        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-4">
            Panier d'achat
          </h1>
          <p className="text-xl text-gray-600 font-light">
            {cartItems.length} article{cartItems.length > 1 ? 's' : ''} dans votre panier
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mt-4 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white border border-gray-100 rounded-2xl shadow-xl overflow-hidden backdrop-blur-sm">
              <div className="p-8">
                <div className="space-y-8">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex items-center space-x-6 pb-8 border-b border-gray-100 last:border-b-0 last:pb-0 group hover:bg-gray-50 rounded-xl p-4 transition-all duration-300">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                          {item.product.images && item.product.images.length > 0 ? (
                            <img
                              src={item.product.images[0].url}
                              alt={item.product.name}
                              className="w-full h-full object-cover rounded-2xl"
                            />
                          ) : (
                            <span className="text-sm text-gray-600 text-center font-medium">
                              {item.product.name}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">{item.product.name}</h3>
                        <p className="text-lg text-gray-500 font-medium mb-2">{item.product.brand.name}</p>
                        <p className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                          {formatPrice(item.product.price)}
                        </p>
                        {item.product.stock_quantity === 0 && (
                          <p className="text-sm text-red-500 mt-2 font-medium">Produit indisponible</p>
                        )}
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-4 bg-gray-50 rounded-xl p-3">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-2 rounded-full bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
                          disabled={!item.inStock}
                        >
                          <Minus className="h-4 w-4 text-gray-600" />
                        </button>
                        <span className="w-16 text-center font-bold text-xl text-gray-900">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-2 rounded-full bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
                          disabled={!item.inStock}
                        >
                          <Plus className="h-4 w-4 text-gray-600" />
                        </button>
                      </div>

                      {/* Total Price */}
                      <div className="text-right">
                        <p className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                          {formatPrice(item.product.price * item.quantity)}
                        </p>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeItem(item.id)}
                        className="p-3 text-red-500 hover:bg-red-50 hover:text-red-600 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md"
                      >
                        <Trash2 className="h-6 w-6" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Continue Shopping */}
            <div className="mt-8">
              <Link
                href="/montres"
                className="inline-flex items-center bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-800 px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
              >
                <ArrowLeft className="h-5 w-5 mr-3" />
                Continuer les achats
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-100 rounded-2xl p-8 sticky top-4 shadow-2xl backdrop-blur-sm">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-8 text-center">
                Résumé de la commande
              </h2>
              


              {/* Price Breakdown */}
              <div className="space-y-4 mb-8 bg-white rounded-xl p-6 shadow-sm">
                <div className="flex justify-between text-lg">
                  <span className="text-gray-700 font-medium">Sous-total</span>
                  <span className="font-bold text-gray-900">{formatPrice(subtotal)}</span>
                </div>

                <div className="flex justify-between text-lg">
                  <span className="text-gray-700 font-medium">Livraison</span>
                  <span className="font-bold text-gray-900">
                    {shipping === 0 ? 'Gratuite' : formatPrice(shipping)}
                  </span>
                </div>

                {shipping === 0 && subtotal > 1000 && (
                  <p className="text-sm text-green-600 bg-green-50 p-2 rounded-lg font-medium">
                    🚚 Livraison gratuite dès 1000 DH d'achat
                  </p>
                )}

                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between text-2xl font-bold">
                    <span className="text-gray-900">Total</span>
                    <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">{formatPrice(total)}</span>
                  </div>
                </div>
              </div>

              {/* Checkout Button */}
              <button
                onClick={handleCheckout}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 px-6 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
              >
                <CreditCard className="h-6 w-6 mr-3" />
                Procéder au paiement
              </button>


            </div>
          </div>
        </div>
      </div>

      {/* Formulaire de livraison */}
      {showCheckoutForm && (
        <div className="fixed inset-0 bg-gradient-to-br from-black/60 to-gray-900/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto border border-white/20">
            {/* Header avec icône */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-t-3xl p-6 text-white text-center relative overflow-hidden">
              <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
              <div className="relative z-10">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                  <CreditCard className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold mb-2">Finaliser votre commande</h2>
                <p className="text-blue-100">Informations de livraison</p>
              </div>
            </div>

            <div className="p-8">

              <form onSubmit={handleSubmitOrder} className="space-y-8">
                {/* Grid pour les champs */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Nom */}
                  <div className="md:col-span-2">
                    <div className="relative">
                      <input
                        type="text"
                        name="nom"
                        value={formData.nom}
                        onChange={handleInputChange}
                        placeholder=" "
                        className="peer w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-blue-500 focus:outline-none bg-white/50 backdrop-blur-sm text-gray-900 font-medium transition-all duration-300 placeholder-transparent"
                        required
                      />
                      <label className="absolute left-4 -top-2.5 bg-white px-2 text-sm font-semibold text-gray-600 transition-all duration-300 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-4 peer-placeholder-shown:bg-transparent peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-blue-600 peer-focus:bg-white">
                        Nom complet *
                      </label>
                    </div>
                  </div>

                  {/* Téléphone */}
                  <div>
                    <div className="relative">
                      <input
                        type="tel"
                        name="telephone"
                        value={formData.telephone}
                        onChange={handleInputChange}
                        placeholder=" "
                        className="peer w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-blue-500 focus:outline-none bg-white/50 backdrop-blur-sm text-gray-900 font-medium transition-all duration-300 placeholder-transparent"
                        required
                      />
                      <label className="absolute left-4 -top-2.5 bg-white px-2 text-sm font-semibold text-gray-600 transition-all duration-300 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-4 peer-placeholder-shown:bg-transparent peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-blue-600 peer-focus:bg-white">
                        Téléphone *
                      </label>
                    </div>
                  </div>

                  {/* Ville */}
                  <div>
                    <div className="relative">
                      <input
                        type="text"
                        name="ville"
                        value={formData.ville}
                        onChange={handleInputChange}
                        placeholder=" "
                        className="peer w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-blue-500 focus:outline-none bg-white/50 backdrop-blur-sm text-gray-900 font-medium transition-all duration-300 placeholder-transparent"
                        required
                      />
                      <label className="absolute left-4 -top-2.5 bg-white px-2 text-sm font-semibold text-gray-600 transition-all duration-300 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-4 peer-placeholder-shown:bg-transparent peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-blue-600 peer-focus:bg-white">
                        Ville *
                      </label>
                    </div>
                  </div>

                  {/* Adresse */}
                  <div className="md:col-span-2">
                    <div className="relative">
                      <input
                        type="text"
                        name="adresse"
                        value={formData.adresse}
                        onChange={handleInputChange}
                        placeholder=" "
                        className="peer w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-blue-500 focus:outline-none bg-white/50 backdrop-blur-sm text-gray-900 font-medium transition-all duration-300 placeholder-transparent"
                        required
                      />
                      <label className="absolute left-4 -top-2.5 bg-white px-2 text-sm font-semibold text-gray-600 transition-all duration-300 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-4 peer-placeholder-shown:bg-transparent peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-blue-600 peer-focus:bg-white">
                        Adresse exacte *
                      </label>
                    </div>
                  </div>
                </div>

                {/* Résumé de la commande */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 shadow-inner">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm font-bold">✓</span>
                    </div>
                    <h3 className="font-bold text-gray-900 text-lg">Résumé de votre commande</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-700 font-medium">Sous-total</span>
                      <span className="font-bold text-gray-900">{formatPrice(subtotal)}</span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-700 font-medium">Livraison</span>
                      <span className="font-bold text-gray-900">{shipping === 0 ? 'Gratuite' : formatPrice(shipping)}</span>
                    </div>
                    <div className="border-t border-blue-200 pt-3 mt-3">
                      <div className="flex justify-between items-center">
                        <span className="text-xl font-bold text-gray-900">Total</span>
                        <span className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">{formatPrice(total)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Boutons */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <button
                    type="button"
                    onClick={() => setShowCheckoutForm(false)}
                    className="flex-1 bg-white border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 text-gray-700 py-4 px-6 rounded-2xl font-bold transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    Retour au panier
                  </button>
                  <button
                    type="submit"
                    className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-4 px-6 rounded-2xl font-bold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 relative overflow-hidden"
                  >
                    <span className="relative z-10">🚀 Confirmer la commande</span>
                    <div className="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  )
}
