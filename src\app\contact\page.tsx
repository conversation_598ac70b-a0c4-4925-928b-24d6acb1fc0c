'use client'

import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Mail, Phone, MapPin, Clock, Send } from 'lucide-react'

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement form submission
    console.log('Form submitted:', formData)
    alert('Merci pour votre message ! Nous vous répondrons dans les plus brefs délais.')
    setFormData({ name: '', email: '', phone: '', subject: '', message: '' })
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
            Contactez-nous
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6 rounded-full"></div>
          <p className="text-2xl text-gray-600 font-light max-w-3xl mx-auto">
            Nous sommes là pour répondre à toutes vos questions avec le plus grand soin
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Information */}
          <div className="bg-gradient-to-br from-white to-gray-50 p-10 rounded-3xl shadow-2xl border border-gray-100">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-8">
              Nos Coordonnées
            </h2>

            <div className="space-y-8">
              <div className="group flex items-start hover:transform hover:scale-105 transition-all duration-300">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-2xl mr-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <Phone className="h-7 w-7 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-1">Téléphone</h3>
                  <p className="text-lg font-semibold text-blue-600">0603357867</p>
                  <p className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full inline-block mt-1">
                    Lun-Sam: 9h-19h
                  </p>
                </div>
              </div>

              <div className="group flex items-start hover:transform hover:scale-105 transition-all duration-300">
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-2xl mr-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <Mail className="h-7 w-7 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-1">Email</h3>
                  <p className="text-lg font-semibold text-purple-600"><EMAIL></p>
                  <p className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full inline-block mt-1">
                    Réponse sous 24h
                  </p>
                </div>
              </div>

              <div className="group flex items-start hover:transform hover:scale-105 transition-all duration-300">
                <div className="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-2xl mr-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <MapPin className="h-7 w-7 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-1">Adresse</h3>
                  <p className="text-lg font-semibold text-green-600">Casablanca - Sidi Bernoussi</p>
                  <p className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full inline-block mt-1">
                    Maroc
                  </p>
                </div>
              </div>

              <div className="group flex items-start hover:transform hover:scale-105 transition-all duration-300">
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-4 rounded-2xl mr-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <Clock className="h-7 w-7 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Horaires d'ouverture</h3>
                  <div className="space-y-1">
                    <p className="text-gray-700 font-medium">Lundi - Vendredi: <span className="text-orange-600 font-bold">9h00 - 19h00</span></p>
                    <p className="text-gray-700 font-medium">Samedi: <span className="text-orange-600 font-bold">9h00 - 17h00</span></p>
                    <p className="text-gray-700 font-medium">Dimanche: <span className="text-red-500 font-bold">Fermé</span></p>
                  </div>
                </div>
              </div>
            </div>

            {/* WhatsApp Contact */}
            <div className="mt-10 p-8 bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl border-2 border-green-200 shadow-xl relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-green-400/10 to-emerald-400/10"></div>
              <div className="relative z-10">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-green-800">Contact WhatsApp</h3>
                </div>
                <p className="text-green-700 mb-6 text-lg font-medium">
                  Pour une réponse rapide, contactez-nous directement sur WhatsApp
                </p>
                <a
                  href="https://wa.me/212603357867"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl"
                >
                  <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                  </svg>
                  Contacter sur WhatsApp
                </a>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-gradient-to-br from-white to-gray-50 p-10 rounded-3xl shadow-2xl border border-gray-100">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-8">
              Envoyez-nous un message
            </h2>

            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-lg font-bold text-gray-800 mb-3">
                    Nom complet *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 bg-white hover:border-gray-300 transition-all duration-300 text-gray-900 font-medium shadow-lg hover:shadow-xl"
                    placeholder="Votre nom complet"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-lg font-bold text-gray-800 mb-3">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 bg-white hover:border-gray-300 transition-all duration-300 text-gray-900 font-medium shadow-lg hover:shadow-xl"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="phone" className="block text-lg font-bold text-gray-800 mb-3">
                  Téléphone
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 bg-white hover:border-gray-300 transition-all duration-300 text-gray-900 font-medium shadow-lg hover:shadow-xl"
                  placeholder="Votre numéro de téléphone"
                />
              </div>

              <div>
                <label htmlFor="subject" className="block text-lg font-bold text-gray-800 mb-3">
                  Sujet *
                </label>
                <select
                  id="subject"
                  name="subject"
                  required
                  value={formData.subject}
                  onChange={handleChange}
                  className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 bg-white hover:border-gray-300 transition-all duration-300 text-gray-900 font-medium shadow-lg hover:shadow-xl appearance-none cursor-pointer"
                >
                  <option value="">Sélectionnez un sujet</option>
                  <option value="info-produit">Information sur un produit</option>
                  <option value="commande">Question sur une commande</option>
                  <option value="livraison">Livraison</option>
                  <option value="retour">Retour/Échange</option>
                  <option value="garantie">Garantie</option>
                  <option value="autre">Autre</option>
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-lg font-bold text-gray-800 mb-3">
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  required
                  rows={6}
                  value={formData.message}
                  onChange={handleChange}
                  className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 bg-white hover:border-gray-300 transition-all duration-300 text-gray-900 font-medium shadow-lg hover:shadow-xl resize-none"
                  placeholder="Décrivez votre demande en détail..."
                />
              </div>

              <button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-5 px-8 rounded-2xl font-bold text-lg transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
              >
                <Send className="h-6 w-6 mr-3" />
                Envoyer le message
              </button>
            </form>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
