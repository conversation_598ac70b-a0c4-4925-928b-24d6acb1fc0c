'use client'

import React, { useState, useEffect, createContext, useContext } from 'react'
import { isDatabaseConfigured } from '@/lib/prisma'

interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
}

interface AuthContextType {
  user: User | null
  profile: any | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error?: any }>
  signUp: (email: string, password: string, userData?: any) => Promise<{ error?: any }>
  signOut: () => Promise<void>
  updateProfile: (data: any) => Promise<boolean>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuthProvider() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!isDatabaseConfigured) {
      setLoading(false)
      return
    }

    // Vérifier l'authentification au chargement
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
        setProfile(data.user)
      }
    } catch (error) {
      console.error('Error checking auth:', error)
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    if (!isDatabaseConfigured) {
      return { error: { message: 'Base de données non configurée. Mode démonstration uniquement.' } }
    }

    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password })
      })

      const data = await response.json()

      if (response.ok) {
        setUser(data.user)
        setProfile(data.user)
        return { error: null }
      } else {
        return { error: { message: data.error } }
      }
    } catch (error) {
      return { error: { message: 'Erreur de connexion' } }
    }
  }

  const signUp = async (email: string, password: string, userData?: any) => {
    if (!isDatabaseConfigured) {
      return { error: { message: 'Base de données non configurée. Mode démonstration uniquement.' } }
    }

    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password, ...userData })
      })

      const data = await response.json()

      if (response.ok) {
        setUser(data.user)
        setProfile(data.user)
        return { error: null }
      } else {
        return { error: { message: data.error } }
      }
    } catch (error) {
      return { error: { message: 'Erreur lors de la création du compte' } }
    }
  }

  const signOut = async () => {
    if (!isDatabaseConfigured) {
      return
    }

    try {
      await fetch('/api/auth/signout', { method: 'POST' })
      setUser(null)
      setProfile(null)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const updateProfile = async (data: any) => {
    if (!user || !isDatabaseConfigured) return false
    
    try {
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (response.ok) {
        await checkAuth() // Recharger les données utilisateur
        return true
      }
      return false
    } catch (error) {
      console.error('Error updating profile:', error)
      return false
    }
  }

  return {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile
  }
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const auth = useAuthProvider()

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  )
}
